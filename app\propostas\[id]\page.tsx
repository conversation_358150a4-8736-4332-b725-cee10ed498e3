"use client";

import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { Protect } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle
} from '@/components/ui/dialog';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  Users, 
  Calendar, 
  User, 
  Plus, 
  Share,
  X,
  ChevronDown,
  Search,
  Filter,
  Check,
  Clock,
  BarChart3,
  ChevronRight,
  Settings,
  DollarSign,
  Eye,
  Shield,
  Download,
  Grid3X3,
  Table as TableIcon,
  // 🆕 NOVOS ÍCONES PARA DIALOG DE CONFIGURAÇÃO
  Lock,
  Paintbrush,
  Loader2,
  Mail,
  UserPlus,
  RefreshCw,
  // 🎨 ÍCONES DUOTONE PARA ACCORDION
  Database,
  MapPin,
  Smartphone,
  Heart,
  TrendingUp,
  Phone,
  Briefcase,
  Activity,
  Save,
  // 🆕 ÍCONE PARA PACOTES
  Package
} from 'lucide-react';

// 🆕 IMPORTAR COMPONENTE DE PERMISSÕES POR EMAIL
import { EmailPermissionsManager } from '@/components/ui/email-permissions-manager';

// Cor centralizada para textos e ícones
const TEXT_COLOR = 'text-[#270038] dark:text-white';

// Ícones das redes sociais
const InstagramIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className={className} style={style}>
    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
  </svg>
);

const YoutubeIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className={className} style={style}>
    <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
  </svg>
);

const TiktokIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 448 512" className={className} style={style}>
    <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
  </svg>
);
import { toast } from '@/components/ui/use-toast';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';

// ✨ IMPORTAR LOADER GLOBAL
import { useGlobalLoader } from '@/components/ui/loader';
import { Loader } from '@/components/ui/loader';

// Chat removido do projeto

// Importar tipos de compartilhamento
import { PermissionLevel } from '@/types/proposal-sharing';

// Importar tipos de pacotes de serviços
import {
  ServicePackage,
  ProposalPackage,
  PackageTemplate,
  CreatePackageInput,
  ApplyPackageInput,
  PackageValidationResult,
  PackageService
} from '@/types/service-package';

// Importar componente de ícones sociais
import { SocialIcon, getPlatformDisplayName } from '@/components/ui/social-icons';

// Importar Firebase
import { ProposalService } from '@/services/proposal-service';
import { 
  Proposal, 
  ProposalStatus, 
  ProposalPriority,
  InfluencerInProposal,
  PROPOSAL_STATUS_LABELS,
  getProposalStatusColor 
} from '@/types/proposal';

// Importar DataTable e Colunas Completas
import { DataTable } from '@/components/ui/data-table';
import * as TableColumns from '@/components/campanhas/table-columns';
import { useDataTable } from '@/hooks/use-data-table';
import { createInfluencerColumns, Influenciador as InfluencerType } from '@/lib/table-columns/influencers';
import { formatters } from '@/lib/table-utils';
import { cn } from '@/lib/utils';

// Hook de autenticação
import { useAuth } from '@/hooks/use-clerk-auth';

// Hook GraphQL para influenciadores
import { useProposalInfluencers } from '@/hooks/use-graphql-influencers';
import { useInfluencersGraphQL } from '@/hooks/use-influencers-graphql';

// Importar componente de card
import { InfluencerCard } from '@/components/influencer-grid/influencer-card';

// Interfaces removidas - orçamentos serão reimplementados

// Usar tipos do Firebase, mas manter compatibilidade com a UI
interface Proposta {
  id: string;
  nome: string;
  descricao?: string;
  criadoPor: string;
  brandId: string;
  grupo?: string;
  priority: ProposalPriority;
  status: ProposalStatus;
  influencers: InfluencerInProposal[];
  totalAmount: number;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  dataEnvio?: string;
  services: any[];
  // Compatibilidade com UI existente
  perfis: PropostaPerfil[];
  criador: string;
  // Estrutura para orçamentos por serviço
  budgets?: {
    [profileId: string]: {
      [service: string]: {
        originalPrice: number;
        budgetedPrice: number;
        updatedAt: Date;
        updatedBy: string;
      };
    };
  };
}

interface PropostaPerfil {
  id: string;
  influencerId: string;
  nomeInfluencer: string;
  username: string;
  plataforma: string;
  seguidores: number;
  status?: 'pendente' | 'aceito' | 'rejeitado' | 'descartado';
  comentarios?: string;
  valor?: number;
  dataResposta?: Date;
}

interface Influencer {
  id: string;
  nome: string;
  verificado?: boolean;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  // Nova estrutura da API
  socialNetworks?: {
    instagram?: {
      username: string;
      followers: number;
      avgViews: number;
      engagementRate: number;
    };
    youtube?: {
      username: string;
      followers: number;
      subscribers: number;
      avgViews: number;
      engagementRate: number;
    };
    tiktok?: {
      username: string;
      followers: number;
      avgViews: number;
      engagementRate: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
  stories_views?: number | string;
  instagram_reels_views?: number | string;
  youtube_insertion_views?: number | string;
  youtube_long_video_views?: number | string;
  youtube_shorts_views?: number | string;
  tiktok_views?: number | string;
  // Campos específicos de visualizações
  instagramStoriesViews?: number | string;
  instagramReelsViews?: number | string;
  tiktokVideoViews?: number | string;
  youtubeShortsViews?: number | string;
  youtubeLongFormViews?: number | string;
  facebookViews?: number | string;
  facebookReelsViews?: number | string;
  facebookStoriesViews?: number | string;
  twitchViews?: number | string;
  kwaiViews?: number | string;
  dadosFinanceiros?: {
    precos?: {
      instagramStory?: { price: number; name?: string };
      instagramReel?: { price: number; name?: string };
      youtubeInsertion?: { price: number; name?: string };
      youtubeDedicated?: { price: number; name?: string };
      youtubeShorts?: { price: number; name?: string };
      tiktokVideo?: { price: number; name?: string };
    };
    prices?: {
      instagramStory?: { price: number; name?: string };
      instagramReel?: { price: number; name?: string };
      youtubeInsertion?: { price: number; name?: string };
      youtubeDedicated?: { price: number; name?: string };
      youtubeShorts?: { price: number; name?: string };
      tiktokVideo?: { price: number; name?: string };
    };
    responsavel?: string;
    agencia?: string;
    whatsappFinanceiro?: string;
    emailFinanceiro?: string;
  };
  // Dados demográficos da audiência
  audienceGender?: {
    instagram?: {
      female: number;
      male: number;
      other: number;
    };
    youtube?: {
      female: number;
      male: number;
      other: number;
    };
    tiktok?: {
      female: number;
      male: number;
      other: number;
    };
    facebook?: {
      female: number;
      male: number;
      other: number;
    };
    twitch?: {
      female: number;
      male: number;
      other: number;
    };
    kwai?: {
      female: number;
      male: number;
      other: number;
    };
  };
  audienceAgeRanges?: {
    instagram?: { ageRange: string; percentage: number }[];
    youtube?: { ageRange: string; percentage: number }[];
    tiktok?: { ageRange: string; percentage: number }[];
    facebook?: { ageRange: string; percentage: number }[];
    twitch?: { ageRange: string; percentage: number }[];
    kwai?: { ageRange: string; percentage: number }[];
  };
  email?: string;
  // Novos campos da API
  country?: string;
  age?: number;
  gender?: string;
  category?: string;
  isVerified?: boolean;
  location?: string;
  phone?: string;
  mainNetwork?: string;
  promotesTraders?: boolean;
  responsibleName?: string;
  agencyName?: string;
  engagementRate?: number;
  // Pricing via GraphQL
  currentPricing?: any;
}

interface Lista {
  id: string;
  nome: string;
  descricao?: string;
  influencers: string[];
  dataCriacao: Date;
  proprietario: string;
}

interface Programa {
  id: string;
  nome: string;
  descricao?: string;
  influencers: PropostaPerfil[];
  dataCriacao: Date;
  status: string;
}

interface OutraProposta {
  id: string;
  nome: string;
  perfis: PropostaPerfil[];
  dataCriacao: Date;
}

// Funções de orçamento removidas - serão reimplementadas

export default function PropostaDetailsPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();

  // CSS customizado para colunas sticky
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
            /* Coluna do influenciador sticky */
      [data-column-id="nomeInfluencer"] {
        position: sticky !important;
        left: 48px !important; /* Após a coluna de checkbox */
        z-index: 10 !important;
        background: hsl(var(--background)) !important;
        border-left: 1px solid hsl(var(--border) / 0.3) !important;
        border-right: 1px solid hsl(var(--border) / 0.3) !important;
     
      }
      
      /* Header da coluna influenciador */
      th[data-column-id="nomeInfluencer"] {
        z-index: 20 !important;
      }
      
      /* Fallback para seletores baseados em posição */
      table th:nth-child(2),
      table td:nth-child(2) {
        position: sticky !important;
        left: 48px !important;
        z-index: 10 !important;
        background: hsl(var(--background)) !important;
        border-left: 1px solid hsl(var(--border) / 0.3) !important;
        border-right: 1px solid hsl(var(--border) / 0.3) !important;
       
      }
      
      table th:nth-child(2) {
        z-index: 20 !important;
      }
      
      /* Garantir que a coluna de checkbox também seja sticky */
      table th:first-child,
      table td:first-child {
        position: sticky !important;
        left: 0 !important;
        z-index: 10 !important;
        background: hsl(var(--background)) !important;
        border-right: 1px solid hsl(var(--border) / 0.3) !important;
      }
      
      table th:first-child {
        z-index: 20 !important;
      }
      
      /* Classes aplicadas diretamente */
      .sticky-column {
        position: sticky !important;
        left: 40px !important;
        z-index: 10 !important;
        background: hsl(var(--background)) !important;
       
      }
      
      .sticky-column-header {
        position: sticky !important;
        left: 40px !important;
        z-index: 20 !important;
        background: hsl(var(--background)) !important;
       
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Força aplicação de sticky nas células após renderização
  React.useEffect(() => {
    const applySticky = () => {
      // Coluna de checkbox (primeira coluna)
      const checkboxCells = document.querySelectorAll('table th:first-child, table td:first-child');
      checkboxCells.forEach((cell: any) => {
        cell.style.position = 'sticky';
        cell.style.left = '0px';
        cell.style.zIndex = cell.tagName === 'TH' ? '20' : '10';
        cell.style.background = 'hsl(var(--background))';
        cell.style.borderRight = '1px solid hsl(var(--border) / 0.3)';
      });

      // Coluna do influenciador (segunda coluna) - calcular largura real da primeira coluna
      const firstColumnElement = document.querySelector('table th:first-child') as HTMLElement | null;
      const firstColumnWidth = firstColumnElement?.offsetWidth ?? 48;
      const influencerCells = document.querySelectorAll('table th:nth-child(2), table td:nth-child(2)');
      influencerCells.forEach((cell: any) => {
        cell.style.position = 'sticky';
        cell.style.left = `${firstColumnWidth}px`;
        cell.style.zIndex = cell.tagName === 'TH' ? '20' : '10';
        cell.style.background = 'hsl(var(--background))';
        cell.style.borderLeft = '1px solid hsl(var(--border) / 0.3)';
        cell.style.borderRight = '1px solid hsl(var(--border) / 0.3)';
      });
    };

    // Aplicar imediatamente
    applySticky();

    // Aplicar após mudanças na tabela (ordenação, filtros, etc)
    const observer = new MutationObserver(applySticky);
    const tableContainer = document.querySelector('[data-testid="data-table"]') || document.querySelector('table');
    
    if (tableContainer) {
      observer.observe(tableContainer, { 
        childList: true, 
        subtree: true 
      });
    }

    return () => {
      observer.disconnect();
    };
  }, []); // Remover dependências que ainda não foram inicializadas
  const propostaId = params?.id as string;
  const brandId = searchParams?.get('brandId') || searchParams?.get('brand');
  
  // Hook de autenticação Clerk
  const { currentUser, isLoading: authLoading, getToken } = useAuth();
  
  // ✨ USAR LOADER GLOBAL EM VEZ DO ESTADO LOCAL
  const { isLoading, showLoader, hideLoader } = useGlobalLoader();

  // Hook GraphQL para buscar influenciadores
  const {
    influencers: graphqlInfluencers,
    loading: loadingInfluencers,
    error: errorInfluencers,
    refetch: refetchInfluencers
  } = useInfluencersGraphQL({
    userId: (currentUser?.id as string) || '',
    autoFetch: true, // ✅ MUDANÇA: Buscar automaticamente quando userId estiver disponível
    filters: {},
    pagination: { limit: 1000 } // Buscar todos os influenciadores
  });
  
  const [proposta, setProposta] = useState<Proposta | null>(null);
  const [influencers, setInfluencers] = useState<Influencer[]>([]);
  const [listas, setListas] = useState<Lista[]>([]);
  const [programas, setProgramas] = useState<Programa[]>([]);
  const [outrasPropostas, setOutrasPropostas] = useState<OutraProposta[]>([]);
  const [showAddInfluencersDialog, setShowAddInfluencersDialog] = useState(false);
  const [selectedInfluencers, setSelectedInfluencers] = useState<string[]>([]);
  const [selectedListas, setSelectedListas] = useState<string[]>([]);
  const [selectedPerfisPrograma, setSelectedPerfisPrograma] = useState<string[]>([]);
  const [selectedPerfisOutraProposta, setSelectedPerfisOutraProposta] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [infoExpanded, setInfoExpanded] = useState(true);
  
  // Estados para seleção de marca
  const [showBrandSelectionDialog, setShowBrandSelectionDialog] = useState(false);
  const [availableBrands, setAvailableBrands] = useState<any[]>([]);
  const [selectedBrandForInfluencers, setSelectedBrandForInfluencers] = useState<string | null>(null);
  const [brandInfluencersLoading, setBrandInfluencersLoading] = useState(false);
  
  // Estados para gerenciar comentários (mantido para compatibilidade)
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [commentValue, setCommentValue] = useState('');
  
  // Estados para Stream Chat
  // Estados do chat removidos
  
  // Estados para orçamentos dos serviços
  const [profileBudgets, setProfileBudgets] = useState<{ [profileId: string]: { [service: string]: number } }>({});
  const [editingBudget, setEditingBudget] = useState<{ profileId: string; service: string; value: number } | null>(null);
  
  // Estados para quantidades dos serviços
  const [serviceQuantities, setServiceQuantities] = useState<{ [profileId: string]: { [service: string]: number } }>({});
  
  // Estados para Compartilhamento
  const [userPermission, setUserPermission] = useState<PermissionLevel>('admin'); // TODO: Determinar permissão real do usuário
  
  // 🔧 NOVOS ESTADOS PARA FLUXO DE COMPARTILHAMENTO EM DUAS ETAPAS
  const [showShareConfigDialog, setShowShareConfigDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [sharingLoading, setSharingLoading] = useState(false);
  
  // 🆕 ESTADOS PARA SISTEMA DE PERMISSÕES POR EMAIL
  const [showEmailPermissionsDialog, setShowEmailPermissionsDialog] = useState(false);
  const [showShareOptionsDialog, setShowShareOptionsDialog] = useState(false);
  const [shareSettings, setShareSettings] = useState({
    isPublic: true,
    requiresPassword: false,
    password: '',
    expiresIn: 30,
    allowDownload: false,
    // 🆕 CONFIGURAÇÕES AVANÇADAS DO SNAPSHOT
    includeFields: {
      personalInfo: true,        // Nome, idade, localização
      socialStats: true,         // Seguidores, engagement
      pricing: true,             // Valores dos serviços
      demographics: false,       // Dados demográficos da audiência
      contactInfo: false,        // WhatsApp, email
      comments: false,           // Comentários internos
      budgets: true,             // Orçamentos da proposta
    },
    branding: {
      includeLogo: true,         // Logo da empresa
      customMessage: '',         // Mensagem personalizada
      hideInternalNotes: true,   // Ocultar notas internas
    }
  });
  
  // Estados para nova tabela
  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);

  // 🆕 ESTADOS PARA PACOTES DE SERVIÇOS
  const [packageMode, setPackageMode] = useState<'individual' | 'packages'>('individual');
  const [showPackageDialog, setShowPackageDialog] = useState(false);
  const [availablePackages, setAvailablePackages] = useState<ServicePackage[]>([]);
  const [proposalPackages, setProposalPackages] = useState<ProposalPackage[]>([]);
  const [packageEditData, setPackageEditData] = useState<{
    influencerId: string;
    influencerName: string;
    packageId?: string;
    isEditing: boolean;
  } | null>(null);
  const [packageTemplates, setPackageTemplates] = useState<PackageTemplate[]>([]);
  const [selectedPackageServices, setSelectedPackageServices] = useState<PackageService[]>([]);
  const [packageFormData, setPackageFormData] = useState({
    name: '',
    description: '',
    totalPrice: 0,
    category: '',
    isTemplate: false
  });

  // 🆕 FUNÇÃO PARA CALCULAR PREÇO TOTAL AUTOMATICAMENTE
  const calculatePackageTotalPrice = useCallback(() => {
    const total = selectedPackageServices.reduce((sum, service) => {
      return sum + (service.unitPrice * service.quantity);
    }, 0);
    setPackageFormData(prev => ({ ...prev, totalPrice: total }));
  }, [selectedPackageServices]);

  // 🆕 RECALCULAR PREÇO TOTAL QUANDO SERVIÇOS MUDAREM
  useEffect(() => {
    calculatePackageTotalPrice();
  }, [calculatePackageTotalPrice]);
  
  // Estado para seleção de influenciadores na tabela
  const [selectedProposalInfluencers, setSelectedProposalInfluencers] = useState<string[]>([]);
  
  // Estado específico para dados dos influenciadores da proposta
  const [proposalInfluencersData, setProposalInfluencersData] = useState<{ [key: string]: Influencer }>({});

  // 🔥 CACHE PARA DADOS DE INFLUENCIADORES (MOVIDO PARA O INÍCIO)
  const influencerCache = useRef<{ [key: string]: { data: Influencer; timestamp: number } }>({});
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

  // Estado para controlar a view (table ou card)
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');

  // Estados para controle de visibilidade das colunas
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<{ [key: string]: boolean }>({});
  const [columnOrder, setColumnOrder] = useState<string[]>([]);

  // Estados para presets personalizados
  const [columnPresets, setColumnPresets] = useState<any[]>([]);
  const [presetName, setPresetName] = useState('');
  const [showCreatePresetModal, setShowCreatePresetModal] = useState(false);
  const [isSavingPreset, setIsSavingPreset] = useState(false);
  const [activePreset, setActivePreset] = useState<string | null>(null);

  // Novos estados para o dialog de orçamento
  const [showBudgetDialog, setShowBudgetDialog] = useState(false);
  const [isSavingBudget, setIsSavingBudget] = useState(false);
  const [budgetEditData, setBudgetEditData] = useState({
    profileId: '',
    service: '',
    currentValue: 0,
    influencerName: '',
    serviceName: '',
    originalPrice: 0,
    currentQuantity: 1
  });
  const [budgetInputValue, setBudgetInputValue] = useState('');
  const [quantityInputValue, setQuantityInputValue] = useState('1');

  // Função para quando a ordem das colunas mudar
  const handleColumnOrderChange = (newOrder: string[]) => {
    // ✨ GARANTIR que a coluna 'select' (checkboxes) sempre fique no início
    const filteredOrder = newOrder.filter(col => col !== 'select');
    const finalOrder = ['select', ...filteredOrder];
    
    setColumnOrder(finalOrder);
    saveColumnSettingsToDb(visibleColumns, finalOrder);
  };

  // Hook para DataTable
  const { dataTableProps } = useDataTable<InfluencerType>({
    enableColumnOrdering: true,
    enableRowSelection: true,
    pageSize: 20
  });

  // 🆕 Função utilitária para formatar valor com quantidade
  const formatValueWithQuantity = (profileId: string, serviceType: string) => {
    const budget = profileBudgets[profileId]?.[serviceType] || 0;
    const quantity = serviceQuantities[profileId]?.[serviceType] || 1;
    
    if (budget === 0) {
      return 'R$ 0';
    }
    
    const total = budget * quantity;
    
    if (quantity > 1) {
      return `${quantity}x R$ ${budget.toLocaleString('pt-BR')} = R$ ${total.toLocaleString('pt-BR')}`;
    } else {
      return `R$ ${budget.toLocaleString('pt-BR')}`;
    }
  };

  // 🆕 Função para exibir apenas o total calculado (sem fórmula)
  const formatTotalValue = (profileId: string, serviceType: string) => {
    const budget = profileBudgets[profileId]?.[serviceType] || 0;
    const quantity = serviceQuantities[profileId]?.[serviceType] || 1;
    
    const total = budget * quantity;
    
    if (quantity > 1) {
      return (
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">({quantity}x)</span>
          <span>R$ {total.toLocaleString('pt-BR')}</span>
        </div>
      );
    } else {
      return `R$ ${total.toLocaleString('pt-BR')}`;
    }
  };

  // Ordem explícita das colunas para garantir agrupamento dos serviços
  const defaultColumnOrder = [
    'select',
        'nomeInfluencer',
    'country',
    'category',
    // Serviços agrupados por plataforma
    'instagram_story',
    'instagram_reel',
    'youtube_insertion',
    'youtube_video',
    'youtube_shorts',
    'tiktok_video',
    'total_budget',
    'criar_pacote', // 🆕 NOVA COLUNA PARA PACOTES
    
    // Seguidores agrupados
    'instagram_followers',
    'tiktok_followers',
    'youtube_followers',
    // Visualizações gerais
    'tiktok_views',
    'youtube_views',
    // Visualizações específicas
    'instagramStoriesViews',
    'instagramReelsViews',
    'tiktokVideoViews',
    'youtubeShortsViews',
    'youtubeLongFormViews',
    'facebookStoriesViews',
    'facebookReelsViews',
    'twitchViews',
    'kwaiViews',
    // Outras colunas
    'plataforma',
    'seguidores',
    'location',
    'age',
    'gender',
    'verified',
    'mainNetwork',
    'engagementRate',
    'promotesTraders',
    'responsibleInfo',
    'contato',
    // Status e ações sempre por último
    'status',
    'acoes'
  ];

  // Configurações padrão das colunas
  const defaultVisibleColumns = defaultColumnOrder.reduce((acc, key) => {
    acc[key] = true;
    return acc;
  }, {} as { [key: string]: boolean });

  // Funções para salvar/carregar configurações
  const saveColumnSettings = (columns: { [key: string]: boolean }, order: string[]) => {
    try {
      localStorage.setItem('influencer-table-columns', JSON.stringify(columns));
      localStorage.setItem('influencer-table-order', JSON.stringify(order));
    } catch (error) {
      console.warn('Erro ao salvar configurações das colunas:', error);
    }
  };

  const loadColumnSettings = () => {
    try {
      const savedColumns = localStorage.getItem('influencer-table-columns');
      const savedOrder = localStorage.getItem('influencer-table-order');
      
      if (savedColumns) {
        const parsedColumns = JSON.parse(savedColumns);
        setVisibleColumns(parsedColumns);
      } else {
        setVisibleColumns(defaultVisibleColumns);
      }
      
      if (savedOrder) {
        const parsedOrder = JSON.parse(savedOrder);
        // ✨ GARANTIR que 'select' sempre seja a primeira coluna
        const filteredOrder = parsedOrder.filter((col: string) => col !== 'select');
        const finalOrder = ['select', ...filteredOrder];
        setColumnOrder(finalOrder);
      } else {
        // Ordem padrão com 'select' primeiro
        const defaultOrder = ['select', ...defaultColumnOrder];
        setColumnOrder(defaultOrder);
      }
    } catch (error) {
      console.warn('Erro ao carregar configurações das colunas:', error);
      setVisibleColumns(defaultVisibleColumns);
      // Ordem padrão com 'select' primeiro
      const defaultOrder = ['select', ...defaultColumnOrder];
      setColumnOrder(defaultOrder);
    }
  };

  // 🔥 EFFECT PARA SINCRONIZAR DADOS DO GRAPHQL COM O ESTADO LOCAL
  useEffect(() => {
    if (graphqlInfluencers && graphqlInfluencers.length > 0) {
      console.log('🔄 Sincronizando dados do GraphQL com estado local...', {
        total: graphqlInfluencers.length
      });
      
      // Converter dados do GraphQL para o formato esperado pela UI
      const adaptedInfluencers = graphqlInfluencers.map((influencer: any) => ({
        ...influencer,
        nome: influencer.name,
        verificado: influencer.isVerified,
        verified: influencer.isVerified || false,
        pais: influencer.country || '',
        cidade: influencer.city || '',
        estado: influencer.state || '',
        idade: influencer.age || 0,
        categoria: influencer.category || '',
        divulgaTrader: influencer.promotesTraders || false,
        genero: influencer.gender as 'Masculino' | 'Feminino' | 'Outro' || 'Outro',
        redesSociais: {
          instagram: influencer.instagramUsername ? {
            username: influencer.instagramUsername,
            seguidores: influencer.instagramFollowers || 0,
            engajamento: influencer.instagramEngagementRate || 0
          } : undefined,
          youtube: influencer.youtubeUsername ? {
            username: influencer.youtubeUsername,
            seguidores: influencer.youtubeFollowers || 0,
            visualizacoes: influencer.youtubeAvgViews || 0
          } : undefined,
          tiktok: influencer.tiktokUsername ? {
            username: influencer.tiktokUsername,
            seguidores: influencer.tiktokFollowers || 0,
            curtidas: influencer.tiktokAvgViews || 0
          } : undefined
        },
        
        // 🔥 ADICIONAR CAMPOS DE VIEWS AQUI TAMBÉM
        instagramStoriesViews: influencer.instagramStoriesViews || 0,
        instagramReelsViews: influencer.instagramReelsViews || 0,
        tiktokVideoViews: influencer.tiktokVideoViews || 0,
        youtubeShortsViews: influencer.youtubeShortsViews || 0,
        youtubeLongFormViews: influencer.youtubeLongFormViews || 0,
        facebookViews: influencer.facebookViews || 0,
        twitchViews: influencer.twitchViews || 0,
        kwaiViews: influencer.kwaiViews || 0,
        
        // Campos alternativos para compatibilidade
        stories_views: influencer.instagramStoriesViews || 0,
        instagram_reels_views: influencer.instagramReelsViews || 0,
        youtube_insertion_views: influencer.youtubeAvgViews || 0,
        youtube_long_video_views: influencer.youtubeLongFormViews || 0,
        youtube_shorts_views: influencer.youtubeShortsViews || 0,
        tiktok_views: influencer.tiktokVideoViews || 0,
        
        servicos: {
          postFeed: 0,
          stories: 0,
          reels: 0,
          videoYoutube: 0,
          videoTiktok: 0
        }
      }));
      
      setInfluencers(adaptedInfluencers);
      console.log('✅ Estado local atualizado com dados do GraphQL');
    }
  }, [graphqlInfluencers]);

  // Carregar quantidades dos serviços do Firebase - MEMOIZADA para evitar loops
  const loadServiceQuantities = useCallback(async () => {
    console.log('📥 Carregando quantidades dos serviços...');
    
    if (!proposta?.perfis || !currentUser) {
      console.log('⚠️ Dados necessários não disponíveis para carregar quantidades');
      return;
    }

    try {
      // 🆕 NOVA IMPLEMENTAÇÃO: Buscar quantidades dos counterProposals
      const localQuantities: { [profileId: string]: { [service: string]: number } } = {};
      const localBudgets: { [profileId: string]: { [service: string]: number } } = {};

      // Para cada perfil da proposta, buscar seus budgets e extrair quantidades dos counterProposals
      for (const perfil of proposta.perfis) {
        const response = await fetch(`/api/budgets?influencerId=${perfil.influencerId}&proposalId=${proposta.id}&userId=${currentUser.id}`);
        
        if (response.ok) {
          const data = await response.json();
          
          if (data.budgets && Array.isArray(data.budgets)) {
            data.budgets.forEach((budget: any) => {
              // Armazenar o valor do budget
              if (!localBudgets[perfil.id]) {
                localBudgets[perfil.id] = {};
              }
              
              const serviceType = budget.serviceType || budget.id;
              
              // 🔥 CORREÇÃO: Se há contraproposta aceita, usar seu valor ao invés do budget original
              let finalBudgetValue = budget.amount || 0;
              
              if (budget.counterProposals && budget.counterProposals.length > 0) {
                // Ordenar contrapropostas por data (mais recente primeiro)
                const sortedCounterProposals = budget.counterProposals.sort((a: any, b: any) => {
                  const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
                  const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
                  return dateB - dateA;
                });

                // Buscar contraproposta aceita mais recente
                const acceptedProposal = sortedCounterProposals.find((cp: any) => cp.status === 'accepted');
                
                if (acceptedProposal) {
                  // 🔧 CORREÇÃO: Verificar tanto counterAmount quanto proposedAmount
                  const proposalValue = acceptedProposal.counterAmount !== undefined 
                    ? acceptedProposal.counterAmount 
                    : acceptedProposal.proposedAmount;
                    
                  if (proposalValue !== undefined) {
                    finalBudgetValue = proposalValue;
                    console.log('💰 [BUDGET-VALUE] Usando valor da contraproposta aceita:', {
                      serviceType,
                      originalBudget: budget.amount,
                      acceptedValue: proposalValue,
                      source: acceptedProposal.counterAmount !== undefined ? 'counterAmount' : 'proposedAmount'
                    });
                  }
                } else {
                  // 🆕 Se não há aceita, usar a contraproposta mais recente (pendente)
                  const latestProposal = sortedCounterProposals[0];
                  if (latestProposal) {
                    const proposalValue = latestProposal.counterAmount !== undefined 
                      ? latestProposal.counterAmount 
                      : latestProposal.proposedAmount;
                      
                    if (proposalValue !== undefined) {
                      finalBudgetValue = proposalValue;
                      console.log('💰 [BUDGET-VALUE] Usando valor da contraproposta mais recente (pendente):', {
                        serviceType,
                        originalBudget: budget.amount,
                        pendingValue: proposalValue,
                        status: latestProposal.status,
                        source: latestProposal.counterAmount !== undefined ? 'counterAmount' : 'proposedAmount'
                      });
                    }
                  }
                }
              }
              
              localBudgets[perfil.id][serviceType] = finalBudgetValue;
              
              // Verificar se há counterProposals com quantidade
              if (budget.counterProposals && budget.counterProposals.length > 0) {
                // 🔥 CORREÇÃO: Garantir que counterProposals estão ordenados (mais recente primeiro)
                const sortedCounterProposals = budget.counterProposals.sort((a: any, b: any) => {
                  const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
                  const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
                  return dateB - dateA; // Mais recente primeiro
                });

                // Primeiro, buscar contraproposta aceita mais recente
                const acceptedProposal = sortedCounterProposals.find((cp: any) => cp.status === 'accepted');
                
                // Se não há aceita, pegar a mais recente com quantidade
                const mostRecentWithQuantity = sortedCounterProposals.find((cp: any) => cp.quantity);
                
                // Priorizar aceita, senão usar a mais recente com quantidade
                const proposalToUse = acceptedProposal || mostRecentWithQuantity;
                
                if (proposalToUse && proposalToUse.quantity) {
                  if (!localQuantities[perfil.id]) {
                    localQuantities[perfil.id] = {};
                  }
                  
                  localQuantities[perfil.id][serviceType] = proposalToUse.quantity;
                  
                  console.log('🎯 [QUANTITY] Usando quantidade da contraproposta:', {
                    serviceType,
                    quantity: proposalToUse.quantity,
                    status: proposalToUse.status,
                    isAccepted: proposalToUse.status === 'accepted',
                    proposedAt: proposalToUse.proposedAt
                  });
                }
              }
            });
          }
        }
      }

      console.log('🎯 Quantidades organizadas por perfil (via counterProposals):', localQuantities);
      console.log('💰 Budgets organizados por perfil:', localBudgets);
      
      setServiceQuantities(localQuantities);
      setProfileBudgets(localBudgets);

    } catch (error) {
      console.error('❌ Erro ao carregar quantidades dos serviços:', error);
      setServiceQuantities({});
      setProfileBudgets({});
    }
  }, [proposta?.perfis, proposta?.id, currentUser?.id]);

  // Carregar configurações na inicialização
  useEffect(() => {
    loadColumnSettings();
  }, []);

  // 🆕 Carregar budgets e quantidades quando proposta for carregada
  useEffect(() => {
    if (proposta?.perfis && currentUser) {
      console.log('📊 [BUDGETS] Carregando budgets e quantidades da proposta...');
      loadServiceQuantities();
    }
  }, [proposta?.perfis, currentUser?.id, loadServiceQuantities]);

  // 🔥 FUNÇÃO OTIMIZADA: Obter dados do influenciador com múltiplas fontes
  const getInfluencerData = (influencerId: string) => {
    // 1. Primeiro, tentar buscar nos dados específicos da proposta (mais atualizado)
    const proposalData = proposalInfluencersData[influencerId];
    if (proposalData && proposalData.nome) {
      return proposalData;
    }
    
    // 2. Verificar cache de influenciadores (dados recém-carregados)
    const cachedData = influencerCache.current[influencerId];
    if (cachedData && cachedData.data && cachedData.data.nome) {
      return cachedData.data;
    }
    
    // 3. Fallback: buscar nos influenciadores globais
    const globalData = influencers.find(inf => inf.id === influencerId);
    if (globalData && globalData.nome) {
      return globalData;
    }
    
    // 4. Fallback final: dados mínimos para evitar crash
    return {
      id: influencerId,
      nome: 'Carregando...',
      verificado: false,
      verified: false,
      categoria: '',
      pais: '',
      cidade: '',
      estado: '',
      idade: 0,
      divulgaTrader: false,
      genero: 'Outro' as const,
      whatsapp: '',
      servicos: {
        postFeed: 0,
        stories: 0,
        reels: 0,
        videoYoutube: 0,
        videoTiktok: 0
      },
      redesSociais: {}
    };
  };

  // 🆕 FUNÇÃO PARA MAPEAR DADOS DA API GRAPHQL 
  const mapGraphQLDataToInfluencer = (gqlData: any): Influencer => {
    return {
      ...gqlData,
      nome: gqlData.name || gqlData.nome,
      avatar: gqlData.avatar,
      verificado: gqlData.isVerified || false,
      verified: gqlData.isVerified || false,
      pais: gqlData.country || gqlData.pais,
      cidade: gqlData.city || gqlData.cidade,
      estado: gqlData.state || gqlData.estado,
      idade: gqlData.age || gqlData.idade,
      categoria: gqlData.category || gqlData.categoria,
      genero: gqlData.gender || gqlData.genero,
      whatsapp: gqlData.whatsapp || gqlData.phone,
      email: gqlData.email,
      divulgaTrader: gqlData.promotesTraders || false,
      promotesTraders: gqlData.promotesTraders || false,
      responsibleName: gqlData.responsibleName,
      agencyName: gqlData.agencyName,
      engagementRate: gqlData.engagementRate,
      // 🎯 MAPEAMENTO CORRETO DOS CAMPOS DE VIEWS DA API GRAPHQL
      instagramStoriesViews: gqlData.instagramStoriesViews,
      instagramReelsViews: gqlData.instagramReelsViews,
      tiktokVideoViews: gqlData.tiktokVideoViews,
      youtubeShortsViews: gqlData.youtubeShortsViews,
      youtubeLongFormViews: gqlData.youtubeLongFormViews,
      facebookViews: gqlData.facebookViews,
      facebookStoriesViews: gqlData.facebookStoriesViews,
      facebookReelsViews: gqlData.facebookReelsViews,
      twitchViews: gqlData.twitchViews,
      kwaiViews: gqlData.kwaiViews,
      // Campos de followers
      instagramFollowers: gqlData.instagramFollowers,
      tiktokFollowers: gqlData.tiktokFollowers,
      youtubeFollowers: gqlData.youtubeFollowers,
      facebookFollowers: gqlData.facebookFollowers,
      // Dados financeiros
      currentPricing: gqlData.currentPricing,
      currentDemographics: gqlData.currentDemographics,
      // Compatibilidade com estrutura antiga
      redesSociais: {
        instagram: gqlData.instagramUsername ? {
          username: gqlData.instagramUsername,
          seguidores: gqlData.instagramFollowers || 0,
          engajamento: gqlData.instagramEngagementRate || 0
        } : undefined,
        tiktok: gqlData.tiktokUsername ? {
          username: gqlData.tiktokUsername,
          seguidores: gqlData.tiktokFollowers || 0,
          curtidas: gqlData.tiktokEngagementRate || 0
        } : undefined,
        youtube: gqlData.youtubeUsername ? {
          username: gqlData.youtubeUsername,
          seguidores: gqlData.youtubeFollowers || 0,
          visualizacoes: gqlData.youtubeAvgViews || 0
        } : undefined
      },
      servicos: {
        postFeed: 0,
        stories: 0,
        reels: 0,
        videoYoutube: 0,
        videoTiktok: 0
      }
    };
  };

  // Função para toggle de visibilidade das colunas
  const toggleColumnVisibility = (columnKey: string) => {
    // Não permitir edição das colunas fixas
    const fixedColumns = ['nomeInfluencer', 'acoes'];
    if (fixedColumns.includes(columnKey)) return;

    const newVisibleColumns = {
      ...visibleColumns,
      [columnKey]: !visibleColumns[columnKey]
    };
    setVisibleColumns(newVisibleColumns);

    // 🎯 LIMPAR PRESET ATIVO QUANDO CONFIGURAÇÃO É ALTERADA MANUALMENTE
    setActivePreset(null);

    saveColumnSettingsToDb(newVisibleColumns, columnOrder);
  };

  // Função para salvar configurações das colunas no banco
  const saveColumnSettingsToDb = async (visible: { [key: string]: boolean }, order: string[]) => {
    try {
      if (!currentUser?.id) return;

      const settingsData = {
        visibleColumns: visible,
        columnOrder: order,
        updatedAt: new Date()
      };

      console.log('💾 [FRONTEND] Salvando configurações das colunas:', {
        userId: currentUser.id,
        proposalId: propostaId,
        hasSettings: !!settingsData
      });

      const response = await fetch('/api/proposals/column-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          proposalId: propostaId,
          settings: settingsData,
          userId: currentUser.id // ✅ ENVIAR USER ID
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ Erro ao salvar configurações das colunas:', errorData);
      } else {
        console.log('✅ Configurações das colunas salvas com sucesso');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar configurações das colunas:', error);
    }
  };

  // Função para carregar configurações das colunas do Firebase
  const loadColumnSettingsFromDb = async () => {
    try {
      if (!currentUser?.id) {
        console.log('⚠️ [COLUMN-SETTINGS] Usuário não logado, usando configurações padrão');
        setDefaultColumnSettings();
        return;
      }

      if (!propostaId) {
        console.log('⚠️ [COLUMN-SETTINGS] PropostaId não disponível');
        setDefaultColumnSettings();
        return;
      }

      console.log('📊 [COLUMN-SETTINGS] Iniciando carregamento das configurações:', {
        userId: currentUser.id,
        proposalId: propostaId,
        apiUrl: `/api/proposals/column-settings?proposalId=${propostaId}&userId=${currentUser.id}`
      });

      const response = await fetch(`/api/proposals/column-settings?proposalId=${propostaId}&userId=${currentUser.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('📊 [COLUMN-SETTINGS] Resposta da API recebida:', {
        status: response.status,
        ok: response.ok
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 [COLUMN-SETTINGS] Dados recebidos:', data);
        
        if (data.success && data.settings) {
          console.log('✅ [COLUMN-SETTINGS] Configurações carregadas com sucesso:', data.settings);
          setVisibleColumns(data.settings.visibleColumns || {});
          
          // 🔧 MESCLAR ordem salva com nova ordem padrão (incluindo novas colunas)
          const savedOrder = data.settings.columnOrder || [];
          const mergedOrder = mergeColumnOrders(savedOrder, defaultColumnOrder);
          const finalOrder = ['select', ...mergedOrder];
          setColumnOrder(finalOrder);
          
          console.log('🔧 [COLUMN-SETTINGS] Configurações aplicadas com merge:', {
            visibleColumns: Object.keys(data.settings.visibleColumns || {}),
            columnOrder: finalOrder,
            savedOrder,
            mergedOrder
          });
        } else {
          console.log('⚙️ [COLUMN-SETTINGS] Nenhuma configuração salva encontrada, usando padrão');
          setDefaultColumnSettings();
        }
      } else {
        const errorData = await response.text();
        console.error('❌ [COLUMN-SETTINGS] Erro HTTP ao carregar configurações:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        setDefaultColumnSettings();
      }
    } catch (error) {
      console.error('❌ [COLUMN-SETTINGS] Erro de exceção ao carregar configurações:', error);
      setDefaultColumnSettings();
    }
  };

  // Função para mesclar ordem salva com nova ordem padrão
  const mergeColumnOrders = (savedOrder: string[], newDefaultOrder: string[]): string[] => {
    // Remover 'select' da ordem salva se existir
    const filteredSavedOrder = savedOrder.filter(col => col !== 'select');
    
    // Criar um mapa da posição de cada coluna na ordem salva
    const savedPositions = new Map<string, number>();
    filteredSavedOrder.forEach((col, index) => {
      savedPositions.set(col, index);
    });
    
    // Separar colunas que existem na ordem salva vs novas colunas
    const existingCols: string[] = [];
    const newCols: string[] = [];
    
    newDefaultOrder.forEach(col => {
      if (savedPositions.has(col)) {
        existingCols.push(col);
      } else {
        newCols.push(col);
      }
    });
    
    // Ordenar colunas existentes pela posição salva
    existingCols.sort((a, b) => {
      const posA = savedPositions.get(a) ?? 999;
      const posB = savedPositions.get(b) ?? 999;
      return posA - posB;
    });
    
    // Inserir novas colunas nas posições corretas baseado no defaultColumnOrder
    const result: string[] = [];
    let existingIndex = 0;
    let newIndex = 0;
    
    for (const col of newDefaultOrder) {
      if (savedPositions.has(col)) {
        // Coluna existente - usar da ordem salva
        result.push(existingCols[existingIndex]);
        existingIndex++;
      } else {
        // Nova coluna - inserir na posição correta
        result.push(newCols[newIndex]);
        newIndex++;
      }
    }
    
    // Garantir que 'acoes' esteja sempre por último
    const finalResult = result.filter(col => col !== 'acoes');
    if (newDefaultOrder.includes('acoes')) {
      finalResult.push('acoes');
    }
    
    console.log('🔄 [MERGE] Mesclando ordens:', {
      savedOrder: filteredSavedOrder,
      newDefaultOrder,
      existingCols,
      newCols,
      finalResult
    });
    
    return finalResult;
  };

  // Função para definir configurações padrão
  const setDefaultColumnSettings = () => {
    setVisibleColumns(defaultVisibleColumns);
    
    // ✨ GARANTIR que 'select' sempre seja a primeira coluna
    const defaultOrder = ['select', ...defaultColumnOrder];
    setColumnOrder(defaultOrder);
  };

  // Nomes amigáveis das colunas
  const columnLabels: { [key: string]: string } = {
    // Dados Básicos
    nomeInfluencer: 'Influenciador',
    age: 'Idade',
    gender: 'Gênero',
    category: 'Categoria',
    verified: 'Exclusivo',
    bio: 'Biografia',
    email: 'Email',
    phone: 'Telefone',
    whatsapp: 'WhatsApp',
    isAvailable: 'Disponível',
    rating: 'Avaliação',
    
    // Localização
    country: 'País',
    state: 'Estado',
    city: 'Cidade',
    location: 'Localização',
    
    // Plataformas
    plataforma: 'Plataforma',
    mainNetwork: 'Rede Principal',
    
    // Seguidores Gerais
    seguidores: 'Seguidores',
    totalFollowers: 'Total Seguidores',
    engagementRate: 'Engajamento',
    
    // Instagram
    instagramUsername: 'Instagram Username',
    instagram_followers: 'Instagram Seguidores',
    instagramFollowers: 'Instagram Seguidores',
    instagramEngagementRate: 'Instagram Engajamento',
    instagramStoriesViews: 'Instagram Stories Views',
    instagramReelsViews: 'Instagram Reels Views',
    instagramAvgViews: 'Instagram Média Views',
    
    // TikTok
    tiktokUsername: 'TikTok Username',
    tiktok_followers: 'TikTok Seguidores',
    tiktokFollowers: 'TikTok Seguidores',
    tiktokEngagementRate: 'TikTok Engajamento',
    tiktok_views: 'TikTok Views',
    tiktokVideoViews: 'TikTok Video Views',
    tiktokAvgViews: 'TikTok Média Views',
    
    // YouTube
    youtubeUsername: 'YouTube Username',
    youtube_followers: 'YouTube Seguidores',
    youtubeFollowers: 'YouTube Seguidores',
    youtubeSubscribers: 'YouTube Inscritos',
    youtubeEngagementRate: 'YouTube Engajamento',
    youtube_views: 'YouTube Views',
    youtubeShortsViews: 'YouTube Shorts Views',
    youtubeLongFormViews: 'YouTube Long Form Views',
    youtubeAvgViews: 'YouTube Média Views',
    
    // Facebook
    facebookUsername: 'Facebook Username',
    facebookFollowers: 'Facebook Seguidores',
    facebookEngagementRate: 'Facebook Engajamento',
    facebookStoriesViews: 'Facebook Stories Views',
    facebookReelsViews: 'Facebook Reels Views',
    facebookAvgViews: 'Facebook Média Views',
    
    // Twitch
    twitchUsername: 'Twitch Username',
    twitchFollowers: 'Twitch Seguidores',
    twitchEngagementRate: 'Twitch Engajamento',
    twitchViews: 'Twitch Views',
    
    // Kwai
    kwaiUsername: 'Kwai Username',
    kwaiFollowers: 'Kwai Seguidores',
    kwaiEngagementRate: 'Kwai Engajamento',
    kwaiViews: 'Kwai Views',
    
    // Profissional
    promotesTraders: 'Divulga Trader',
    responsibleName: 'Responsável',
    responsibleInfo: 'Info Responsável',
    agencyName: 'Agência',
    responsibleCapturer: 'Captador',
    contato: 'Contato',
    
    // Serviços/Preços
    instagram_story: 'Stories',
    instagram_reel: 'Reels',
    youtube_insertion: 'Inserção',
    youtube_video: 'Dedicado',
    youtube_shorts: 'Shorts',
    tiktok_video: 'Vídeo',
    facebook_post: 'Facebook Post',
    twitch_stream: 'Twitch Stream',
    kwai_video: 'Kwai Vídeo',
    total_budget: 'Total',
    criar_pacote: 'Criar Pacote', // 🆕 NOVA COLUNA
    
    // Status e Ações
    status: 'Status',
    createdAt: 'Data Criação',
    updatedAt: 'Última Atualização',
    acoes: 'Ações',
  };

  // Função para obter o nome do serviço da API
  const getServiceName = (serviceKey: string): string => {
    // Mapear keys para nomes padrões caso não encontre na API
    const defaultNames: { [key: string]: string } = {
      'instagramStory': 'STORIES',
      'instagramReel': 'REELS', 
      'youtubeDedicated': 'DEDICADO',
      'youtubeShorts': 'SHORTS',
      'tiktokVideo': 'TIKTOK',
      'youtubeInsertion': 'INSERÇÃO'
    };

    // Tentar obter o nome de qualquer influenciador da proposta
    if (proposta?.perfis && proposta.perfis.length > 0) {
      for (const perfil of proposta.perfis) {
        const influencerData = getInfluencerData(perfil.influencerId);
        const financialData = influencerData?.dadosFinanceiros;
        
        // Verificar na estrutura precos
        const serviceName = financialData?.precos?.[serviceKey as keyof typeof financialData.precos]?.name ||
                           (financialData as any)?.prices?.[serviceKey]?.name;
        
        if (serviceName) {
          return serviceName.toUpperCase();
        }
      }
    }

    // Fallback para nome padrão
    return defaultNames[serviceKey] || serviceKey.toUpperCase();
  };

  // Funções auxiliares para formatação
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Colunas da tabela para dados de perfis da proposta - todas as colunas
  const allInfluencerColumns = useMemo(() => [
    {
      accessorKey: "nomeInfluencer",
      header: () => (
        <div style={{ 
          position: 'sticky',
          left: '48px', // Largura padrão da coluna checkbox
          zIndex: 20,
          background: 'hsl(var(--background))',
          borderLeft: '1px solid hsl(var(--border) / 0.3)',
          borderRight: '1px solid hsl(var(--border) / 0.3)',
          paddingRight: '1rem'
        }}>
          <span style={{ color: TEXT_COLOR }}>INFLUENCIADOR</span>
        </div>
      ),
      meta: {
        sticky: 'left',
        stickyOffset: 40, // Offset para ficar após a coluna de checkbox
        headerStyle: {
          position: 'sticky',
          left: '40px',
          zIndex: 20,
          background: 'hsl(var(--background))',
          borderRight: '1px solid hsl(var(--border) / 0.5)'
        },
        cellStyle: {
          position: 'sticky',
          left: '40px',
          zIndex: 10,
          background: 'hsl(var(--background))',
          borderRight: '1px solid hsl(var(--border) / 0.5)'
        }
      },
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        return (
          <div 
            className="flex-1 min-w-0 flex items-center justify-end space-x-3 min-w-[180px]" 
            style={{ 
              position: 'sticky',
              left: '48px', // Largura padrão da coluna checkbox
              zIndex: 10,
              background: 'hsl(var(--background))',
              borderLeft: '1px solid hsl(var(--border) / 0.3)',
              borderRight: '1px solid hsl(var(--border) / 0.3)',
              paddingRight: '1rem',
              marginLeft: '-1rem', // Compensar padding do TD
              paddingLeft: '1rem'
            }}
          >
            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {influencerData?.avatar ? (
                <img 
                  src={influencerData.avatar} 
                  alt={perfil.nomeInfluencer}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Em caso de erro, mostrar fallback
                    e.currentTarget.style.display = 'none';
                    const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}
              <div 
                className={`w-full h-full bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white flex items-center justify-center text-xs font-medium ${influencerData?.avatar ? 'hidden' : 'flex'}`}
                style={{ display: influencerData?.avatar ? 'none' : 'flex' }}
              >
                <User className="h-4 w-4 text-white" />
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-left">{perfil.nomeInfluencer}</div>
              <div className="text-xs text-left text-muted-foreground">{perfil.username}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "plataforma",
      header: () => <span style={{ color: TEXT_COLOR }}>PLATAFORMA</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        if (!influencerData) {
          return <span className="text-sm text-muted-foreground">Multi</span>;
        }
        
        const platforms = [];
        if (influencerData.redesSociais?.instagram) {
          platforms.push({ 
            type: 'instagram', 
            icon: <InstagramIcon style={{ color: TEXT_COLOR }} />,
            title: 'Instagram'
          });
        }
        if (influencerData.redesSociais?.youtube) {
          platforms.push({ 
            type: 'youtube', 
            icon: <YoutubeIcon style={{ color: TEXT_COLOR }} />,
            title: 'YouTube'
          });
        }
        if (influencerData.redesSociais?.tiktok) {
          platforms.push({ 
            type: 'tiktok', 
            icon: <TiktokIcon style={{ color: TEXT_COLOR }} />,
            title: 'TikTok'
          });
        }
        
        return (
          <div className="flex items-center justify-end gap-2">
            {platforms.map((platform, index) => (
              <div 
                key={index} 
                className="flex items-center justify-center w-6 h-6 "
                title={platform.title}
              >
                {platform.icon}
              </div>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "seguidores",
      header: () => <span style={{ color: TEXT_COLOR }}>SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        if (!influencerData) {
          return (
            <div className="text-sm min-w-[120px]">
              <div className="font-medium text-sm text-[#ec003f]">
                {formatNumber(perfil.seguidores)}
              </div>
              <div className="text-xs text-muted-foreground/70">total</div>
            </div>
          );
        }
        
        const maxFollowers = Math.max(
          influencerData.redesSociais?.instagram?.seguidores || 0,
          influencerData.redesSociais?.youtube?.seguidores || 0,
          influencerData.redesSociais?.tiktok?.seguidores || 0
        );
        
        return (
          <div className="text-sm min-w-[120px]">
            <div className="font-medium text-sm text-[#ec003f]">
              {formatNumber(maxFollowers)}
            </div>
            <div className="text-xs text-muted-foreground/70">maior rede</div>
          </div>
        );
      },
    },
    {
      accessorKey: "country",
      header: () => <span style={{ color: TEXT_COLOR }}>PAÍS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        return (
          <div className="min-w-[100px] text-right">
            <span className="text-sm dark:text-white" style={{ color: TEXT_COLOR }}>
              {influencerData?.country || influencerData?.pais || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "location",
      header: () => <span style={{ color: TEXT_COLOR }}>LOCALIZAÇÃO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const location = influencerData?.location || 
                        (influencerData?.cidade && influencerData?.estado ? 
                         `${influencerData.cidade}/${influencerData.estado}` : null);
        
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {location || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "age",
      header: () => <span style={{ color: TEXT_COLOR }}>IDADE</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        return (
          <div className="min-w-[80px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {influencerData?.age || influencerData?.idade || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "gender",
      header: () => <span style={{ color: TEXT_COLOR }}>GÊNERO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const gender = influencerData?.gender || influencerData?.genero;
        const genderText = gender === 'female' ? 'Feminino' : 
                          gender === 'male' ? 'Masculino' : 
                          gender === 'Feminino' ? 'Feminino' :
                          gender === 'Masculino' ? 'Masculino' :
                          gender || 'N/A';
        
        return (
          <div className="min-w-[100px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {genderText}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: () => <span style={{ color: TEXT_COLOR }}>CATEGORIA</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        return (
          <div className="min-w-[120px] text-right">
            <span className="text-sm" style={{ color: TEXT_COLOR }}>
              {influencerData?.category || influencerData?.categoria || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "verified",
      header: () => <span style={{ color: TEXT_COLOR }}>EXCLUSIVO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const isVerified = influencerData?.isVerified || influencerData?.verificado || influencerData?.verified;
        
        return (
          <div className="min-w-[100px] text-right">
            {isVerified ? (
              <div className="flex items-center justify-end gap-1">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-600">Sim</span>
              </div>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>Não</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "mainNetwork",
      header: () => <span style={{ color: TEXT_COLOR }}>REDE PRINCIPAL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const mainNetwork = influencerData?.mainNetwork;
        const networkIcon = mainNetwork === 'instagram' ? <InstagramIcon style={{ color: TEXT_COLOR }} /> :
                           mainNetwork === 'tiktok' ? <TiktokIcon style={{ color: TEXT_COLOR }} /> :
                           mainNetwork === 'youtube' ? <YoutubeIcon style={{ color: TEXT_COLOR }} /> :
                           null;
        
        return (
          <div className="min-w-[120px] text-right">
            {networkIcon ? (
              <div className="flex items-center justify-center gap-2">
                <div className="flex items-center justify-center w-5 h-5">
                  {networkIcon}
                </div>
                <span className="text-sm capitalize" style={{ color: TEXT_COLOR }}>
                  {mainNetwork}
                </span>
              </div>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "engagementRate",
      header: () => <span style={{ color: TEXT_COLOR }}>ENGAJAMENTO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const engagementRate = influencerData?.engagementRate;
        
        return (
          <div className="min-w-[120px] text-right">
            {engagementRate ? (
              <div className="text-sm">
                <span className="font-medium text-[#ec003f]">
                  {typeof engagementRate === 'number' ? `${engagementRate.toFixed(2)}%` : engagementRate}
                </span>
              </div>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "promotesTraders",
      header: () => <span style={{ color: TEXT_COLOR }}>DIVULGA TRADER</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const promotesTraders = influencerData?.promotesTraders ?? influencerData?.divulgaTrader;
        
        return (
          <div className="min-w-[120px] text-right">
            {promotesTraders === true ? (
              <span className="text-sm text-green-600">Sim</span>
            ) : promotesTraders === false ? (
              <span className="text-sm text-red-600">Não</span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "responsibleInfo",
      header: () => <span style={{ color: TEXT_COLOR }}>RESPONSÁVEL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // ✅ CAMPOS DINÂMICOS: Só exibir se disponível no snapshot
        // ✅ Usar APENAS os dados do snapshot (sem fallbacks para campos administrativos)
        const responsibleName = influencerData?.responsibleName;
        const agencyName = influencerData?.agencyName;
        
        return (
          <div className="min-w-[150px] text-right">
            <Protect role="org:admin">
              {responsibleName || agencyName ? (
                <div className="text-sm">
                  {responsibleName && (
                    <div>
                      <span className="text-muted-foreground">Resp:</span> <span style={{ color: TEXT_COLOR }}>{responsibleName}</span>
                    </div>
                  )}
                  {agencyName && (
                    <div>
                      <span className="text-muted-foreground">Agência:</span> <span style={{ color: TEXT_COLOR }}>{agencyName}</span>
                    </div>
                  )}
                </div>
              ) : null /* ✅ Se não há dados administrativos, não exibir nada */}
            </Protect>
          </div>
        );
      },
    },
    {
      accessorKey: "contato",
      header: () => <span style={{ color: TEXT_COLOR }}>CONTATO</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        if (!influencerData) {
          return null; // ✅ Não exibir componente se não há dados
        }
        
        // ✅ CAMPOS DINÂMICOS: Só exibir se disponível no snapshot
        // ✅ Usar APENAS os dados do snapshot (sem fallbacks para outras fontes)
        const whatsapp = influencerData.whatsapp;
        const email = influencerData.email;
        
        return (
          <div className="min-w-[200px] text-right"> 
            {whatsapp && (
              <div className="text-sm">
                <span className="text-muted-foreground">WhatsApp:</span> <span style={{ color: TEXT_COLOR }}>{whatsapp}</span>
              </div>
            )}
            {email && (
              <div className="text-sm">
                <span className="text-muted-foreground">Email:</span> <span style={{ color: TEXT_COLOR }}>{email}</span>
              </div>
            )}
            {/* ✅ Se não há contato, não exibir nada */}
          </div>
        );
      },
    },
    // COLUNAS DE REDES SOCIAIS - FOLLOWERS E VIEWS
    {
      accessorKey: "instagram_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const followers = influencerData?.socialNetworks?.instagram?.followers || 
                         influencerData?.redesSociais?.instagram?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-right">
            {followers > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const followers = influencerData?.socialNetworks?.tiktok?.followers || 
                         influencerData?.redesSociais?.tiktok?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-right">
            {followers > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_views",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const avgViews = influencerData?.socialNetworks?.tiktok?.avgViews || 
                        influencerData?.redesSociais?.tiktok?.curtidas || 0;
        
        return (
          <div className="min-w-[150px] text-right">
            {avgViews > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(avgViews)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_followers",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const followers = influencerData?.socialNetworks?.youtube?.followers || 
                         influencerData?.socialNetworks?.youtube?.subscribers ||
                         influencerData?.redesSociais?.youtube?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-right">
            {followers > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_views",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const avgViews = influencerData?.socialNetworks?.youtube?.avgViews || 
                        influencerData?.redesSociais?.youtube?.visualizacoes || 0;
        
        return (
          <div className="min-w-[150px] text-right">
            {avgViews > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(avgViews)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    // COLUNAS ESPECÍFICAS DE VISUALIZAÇÕES
    {
      accessorKey: "instagramStoriesViews",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM STORIES VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const storiesViews = influencerData?.instagramStoriesViews || 0;
        
        const storiesViewsNum = typeof storiesViews === 'string' ? parseInt(storiesViews.replace(/\./g, '')) : storiesViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {storiesViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(storiesViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "instagramReelsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>INSTAGRAM REELS VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const reelsViews = influencerData?.instagramReelsViews || 0;
        
        const reelsViewsNum = typeof reelsViews === 'string' ? parseInt(reelsViews.replace(/\./g, '')) : reelsViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {reelsViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(reelsViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "tiktokVideoViews",
      header: () => <span style={{ color: TEXT_COLOR }}>TIKTOK VIDEO VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const tiktokViews = influencerData?.tiktokVideoViews || 0;
         
        const tiktokViewsNum = typeof tiktokViews === 'string' ? parseInt(tiktokViews.replace(/\./g, '')) : tiktokViews;
         
        return (
          <div className="min-w-[150px] text-right">
            {tiktokViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(tiktokViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtubeShortsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE SHORTS VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const shortsViews = influencerData?.youtubeShortsViews || 0;
         
        const shortsViewsNum = typeof shortsViews === 'string' ? parseInt(shortsViews.replace(/\./g, '')) : shortsViews;
         
        return (
          <div className="min-w-[150px] text-right">
            {shortsViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(shortsViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtubeLongFormViews",
      header: () => <span style={{ color: TEXT_COLOR }}>YOUTUBE VIEWS VIDEOS LONGOS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const longFormViews = influencerData?.youtubeLongFormViews || 0;
         
        const longFormViewsNum = typeof longFormViews === 'string' ? parseInt(longFormViews.replace(/\./g, '')) : longFormViews;
         
        return (
            <div className="min-w-[150px] text-right">
            {longFormViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(longFormViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },

    {
      accessorKey: "facebookStoriesViews",
      header: () => <span style={{ color: TEXT_COLOR }}>FACEBOOK STORIES VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const fbStoriesViews = influencerData?.facebookStoriesViews || 0;
        
        const fbStoriesViewsNum = typeof fbStoriesViews === 'string' ? parseInt(fbStoriesViews.replace(/\./g, '')) : fbStoriesViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {fbStoriesViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(fbStoriesViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "facebookReelsViews",
      header: () => <span style={{ color: TEXT_COLOR }}>FACEBOOK REELS VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const fbReelsViews = influencerData?.facebookReelsViews || 0;
        
        const fbReelsViewsNum = typeof fbReelsViews === 'string' ? parseInt(fbReelsViews.replace(/\./g, '')) : fbReelsViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {fbReelsViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(fbReelsViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "twitchViews",
      header: () => <span style={{ color: TEXT_COLOR }}>TWITCH VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const twitchViews = influencerData?.twitchViews || 0;
        
        const twitchViewsNum = typeof twitchViews === 'string' ? parseInt(twitchViews.replace(/\./g, '')) : twitchViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {twitchViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(twitchViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "kwaiViews",
      header: () => <span style={{ color: TEXT_COLOR }}>KWAI VIEWS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);
        
        const kwaiViews = influencerData?.kwaiViews || 0;
        
        const kwaiViewsNum = typeof kwaiViews === 'string' ? parseInt(kwaiViews.replace(/\./g, '')) : kwaiViews;
        
        return (
          <div className="min-w-[150px] text-right">
            {kwaiViewsNum > 0 ? (
              <span className="text-sm font-medium" style={{ color: TEXT_COLOR }}>
                {formatNumber(kwaiViewsNum)}
              </span>
            ) : (
              <span className="text-sm" style={{ color: TEXT_COLOR }}>N/A</span>
            )}
          </div>
        );
      },
    },
    // COLUNAS DE SERVIÇOS DINÂMICAS
    {
      accessorKey: "instagram_story",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('instagramStory')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.instagram?.story?.price || 
                             influencerData?.dadosFinanceiros?.precos?.instagramStory?.price || 
                             influencerData?.dadosFinanceiros?.prices?.instagramStory?.price || 0;
        
        // 🔧 CORRIGIR: Buscar valor correto das contrapropostas
        let budgetedPrice = profileBudgets[perfil.id]?.instagram_story || 0;
        
        // Simular busca de contrapropostas (como não temos acesso direto aqui, usar o valor atual)
        // A lógica correta já está na função loadServiceQuantities, mas precisamos garantir
        // que os valores sejam atualizados adequadamente
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.instagram_story || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'instagram_story', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "instagram_reel",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('instagramReel')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.instagram?.reel?.price || 
                             influencerData?.dadosFinanceiros?.precos?.instagramReel?.price || 
                             influencerData?.dadosFinanceiros?.prices?.instagramReel?.price || 0;
        
        const budgetedPrice = profileBudgets[perfil.id]?.instagram_reel || 0;
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.instagram_reel || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'instagram_reel', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_insertion",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('youtubeInsertion')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.youtube?.insertion?.price || 
                             influencerData?.dadosFinanceiros?.precos?.youtubeInsertion?.price || 
                             influencerData?.dadosFinanceiros?.prices?.youtubeInsertion?.price || 0;
        
        const budgetedPrice = profileBudgets[perfil.id]?.youtube_insertion || 0;
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.youtube_insertion || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'youtube_insertion', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_video",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('youtubeDedicated')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.youtube?.dedicated?.price || 
                             influencerData?.dadosFinanceiros?.precos?.youtubeDedicated?.price || 
                             influencerData?.dadosFinanceiros?.prices?.youtubeDedicated?.price || 0;
        
        const budgetedPrice = profileBudgets[perfil.id]?.youtube_video || 0;
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.youtube_video || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'youtube_video', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_shorts",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('youtubeShorts')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.youtube?.shorts?.price || 
                             influencerData?.dadosFinanceiros?.precos?.youtubeShorts?.price || 
                             influencerData?.dadosFinanceiros?.prices?.youtubeShorts?.price || 0;
        const budgetedPrice = profileBudgets[perfil.id]?.youtube_shorts || 0;
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.youtube_shorts || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'youtube_shorts', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
        {
      accessorKey: "tiktok_video",
      header: () => <span style={{ color: TEXT_COLOR }}>{getServiceName('tiktokVideo')}</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
        // Obter dados do influenciador usando a nova estrutura GraphQL
        const influencerData = getInfluencerData(perfil.influencerId);
        
        // Acessar preços via currentPricing (nova estrutura GraphQL)
        const originalPrice = influencerData?.currentPricing?.services?.tiktok?.video?.price || 
                             influencerData?.dadosFinanceiros?.precos?.tiktokVideo?.price || 
                             influencerData?.dadosFinanceiros?.prices?.tiktokVideo?.price || 0;
        
        const budgetedPrice = profileBudgets[perfil.id]?.tiktok_video || 0;
        
        // Quantidade do serviço (agora editada no modal)
        const quantity = serviceQuantities[perfil.id]?.tiktok_video || 1;
        
        return (
          <div className="min-w-[180px]">
            {originalPrice > 0 ? (
              <div className="text-sm space-y-1">
                {/* Preço unitário */}
                <div className="text-gray-600 dark:text-gray-400">
                  {formatCurrency(originalPrice)} 
                </div>
                
                {/* Valor total clicável */}
                <div 
                  className="text-green-600 dark:text-green-400 font-medium cursor-pointer hover:bg-green-50 dark:hover:bg-green-900/20 px-1 py-0.5 rounded"
                  onClick={() => handleEditBudget(perfil.id, 'tiktok_video', budgetedPrice)}
                >
                  {budgetedPrice > 0 ? (
                    <div className="text-sm text-green-600 font-medium">
                      {quantity > 1 && <span className="text-sm text-gray-500 mr-1">{quantity}x</span>}
                      {formatCurrency(budgetedPrice * quantity)}
                    </div>
                  ) : (
                    '+ orçar'
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "total_budget",
      header: () => <span style={{ color: TEXT_COLOR }}>TOTAL</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;

        const budgets = profileBudgets[perfil.id] || {};
        const totalBudgeted = Object.values(budgets)
          .filter((value): value is number => typeof value === 'number' && value > 0)
          .reduce((sum: number, value: number) => sum + value, 0);

        return (
          <div className="min-w-[100px]">
            {totalBudgeted > 0 ? (
              <div className="font-semibold text-[#ec003f]">
                {formatCurrency(totalBudgeted)}
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">R$ 0,00</span>
            )}
          </div>
        );
      },
    },
    // 🆕 NOVA COLUNA: CRIAR PACOTE
    {
      accessorKey: "criar_pacote",
      header: () => <span style={{ color: TEXT_COLOR }}>CRIAR PACOTE</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const influencerData = getInfluencerData(perfil.influencerId);

        // Verificar se há pacotes aplicados para este influenciador
        const influencerPackages = proposalPackages.filter(pkg => pkg.influencerId === perfil.influencerId);

        return (
          <div className="min-w-[120px] space-y-2">
            {/* Botão para criar novo pacote */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setPackageEditData({
                  influencerId: perfil.influencerId,
                  influencerName: perfil.nomeInfluencer,
                  isEditing: false
                });
                setShowPackageDialog(true);
              }}
              className="w-full text-xs h-7 border-[#5600ce]/30 text-[#5600ce] hover:bg-[#5600ce]/10"
            >
              <Plus className="h-3 w-3 mr-1" />
              Criar Pacote
            </Button>

            {/* Exibir pacotes existentes */}
            {influencerPackages.length > 0 && (
              <div className="space-y-1">
                {influencerPackages.slice(0, 2).map((pkg) => (
                  <div
                    key={pkg.id}
                    className="text-xs p-1 bg-muted/50 rounded border"
                  >
                    <div className="font-medium truncate">{pkg.packageSnapshot.name}</div>
                    <div className="text-[#ec003f] font-semibold">
                      {formatCurrency(pkg.finalPrice)}
                    </div>
                  </div>
                ))}
                {influencerPackages.length > 2 && (
                  <div className="text-xs text-muted-foreground">
                    +{influencerPackages.length - 2} mais
                  </div>
                )}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: () => <span style={{ color: TEXT_COLOR }}>STATUS</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        const status = perfil.proposta_status || 'pendente';
        
        const statusConfig = {
          'pendente': { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300', label: 'Pendente' },
          'aceito': { color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300', label: 'Aceito' },
          'rejeitado': { color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300', label: 'Rejeitado' },
          'descartado': { color: 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300', label: 'Descartado' },
        };
        
        const config = statusConfig[status as keyof typeof statusConfig] || 
                      statusConfig.pendente;
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color} whitespace-nowrap`}>
            {config.label}
          </span>
        );
      },
    },
    {
      accessorKey: "acoes",
      header: () => <span style={{ color: TEXT_COLOR }}>AÇÕES</span>,
      cell: ({ row }: any) => {
        const perfil = row.original;
        
          return (
              <div className="flex items-center gap-2 text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <span className="sr-only">Abrir menu</span>
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleStatusChange(perfil.id, 'aceito')}>
                  <Check className="h-4 w-4 mr-2 text-green-600" />
                  Aceitar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange(perfil.id, 'rejeitado')}>
                  <X className="h-4 w-4 mr-2 text-red-600" />
                  Rejeitar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange(perfil.id, 'pendente')}>
                  <Clock className="h-4 w-4 mr-2 text-yellow-600" />
                  Pendente
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleRemoveProfile(perfil.id)} className="text-red-600">
                  <X className="h-4 w-4 mr-2" />
                  Remover
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            </div>
        );
      },
    },
  ], [propostaId, proposta?.nome, proposalInfluencersData, profileBudgets]);

  // Colunas filtradas baseadas na visibilidade
  const influencerColumns = useMemo(() => {
    return allInfluencerColumns
      .filter(column => {
        const key = column.accessorKey;
        // Colunas fixas que sempre aparecem
        const fixedColumns = ['nomeInfluencer', 'acoes'];
        return fixedColumns.includes(key) || visibleColumns[key] !== false;
      });
  }, [allInfluencerColumns, visibleColumns]);

  // Estado para controle de lazy loading
  const [dataLoaded, setDataLoaded] = useState({
    proposta: false,
    brands: false,
    influencers: false,
    listas: false,
    programas: false,
    outrasPropostas: false
  });
  
  // ✅ Estado para controlar se os dados da proposta já foram carregados com sucesso
  const [proposalLoaded, setProposalLoaded] = useState(false);

  // ✅ FUNÇÃO MELHORADA: Forçar reload com limpeza de cache 
  const forceReloadProposal = async () => {
    console.log('🔄 [PROPOSTA] Forçando reload completo dos dados...');
    
    try {
      // Limpar todo o cache de influenciadores
      influencerCache.current = {};
      console.log('🧹 Cache de influenciadores limpo');
      
      // Resetar estado para permitir novo carregamento
      setProposalLoaded(false);
      
      // Forçar reload imediato
      await loadProposta();
      setProposalLoaded(true);
      
      toast({
        title: "Dados atualizados",
        description: "Os dados da proposta foram recarregados com sucesso.",
      });
      
    } catch (error) {
      console.error('❌ Erro ao forçar reload:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar os dados.",
        variant: "destructive",
      });
    }
  };

  // ✅ Carregar dados apenas quando necessário
  useEffect(() => {
    console.log('🔍 [PROPOSTA_DETAILS] useEffect loadProposta:', {
      propostaId,
      proposalLoaded,
      hasPropostaData: !!proposta
    });

    // 🔒 VERIFICAÇÃO: Só carregar se temos ID, usuário autenticado e dados ainda não foram carregados
    if (propostaId && currentUser?.id && !proposalLoaded && !authLoading) {
      console.log('✅ [PROPOSTA_DETAILS] Carregando dados da proposta:', propostaId, 'para usuário:', currentUser.id);
      loadProposta().then(() => {
        setDataLoaded(prev => ({ ...prev, proposta: true }));
        setProposalLoaded(true); // ✅ Marcar como carregado com sucesso
      }).catch((error) => {
        console.error('❌ [PROPOSTA_DETAILS] Erro ao carregar proposta:', error);
        setProposalLoaded(false); // ✅ Permitir retry em caso de erro
      });
    } else if (propostaId && proposalLoaded) {
      console.log('ℹ️ [PROPOSTA_DETAILS] Dados já carregados, evitando reload desnecessário');
    } else if (propostaId && !currentUser?.id && !authLoading) {
      console.warn('⚠️ [PROPOSTA_DETAILS] Usuário não autenticado - aguardando login...');
    }
  }, [propostaId, proposalLoaded, currentUser?.id, authLoading]);

// ✅ USEEFFECT SEPARADO: Carregar configurações das colunas
useEffect(() => {
  if (propostaId && currentUser?.id) {
    console.log('🔧 [COLUMN-SETTINGS] Iniciando carregamento das configurações das colunas');
    loadColumnSettingsFromDb().catch(error => {
      console.error('❌ [COLUMN-SETTINGS] Erro ao carregar configurações:', error);
    });
  }
}, [propostaId, currentUser?.id]);

// ✅ USEEFFECT: Carregar presets personalizados
useEffect(() => {
  if (currentUser?.id) {
    loadColumnPresets();
  }
}, [currentUser?.id]);

  // Carregar influenciadores da marca quando disponível
  useEffect(() => {
    if (brandId && currentUser && !authLoading) {
      // Os influenciadores da proposta são carregados através do loadProposta()
      console.log('🔍 BrandId disponível:', brandId);
    }
  }, [brandId, currentUser, authLoading]);

  // Lazy loading para dados auxiliares (carregados sob demanda)
  const loadAuxiliaryData = async (type: 'listas' | 'programas' | 'outrasPropostas') => {
    if (dataLoaded[type]) return; // Já carregado
    
    try {
      switch (type) {
        case 'listas':
          // TODO: Implementar carregamento de listas
          setListas([]);
          setDataLoaded(prev => ({ ...prev, listas: true }));
          break;
        case 'programas':
          // TODO: Implementar carregamento de programas
          setProgramas([]);
          setDataLoaded(prev => ({ ...prev, programas: true }));
          break;
        case 'outrasPropostas':
          // TODO: Implementar carregamento de outras propostas
          setOutrasPropostas([]);
          setDataLoaded(prev => ({ ...prev, outrasPropostas: true }));
          break;
      }
    } catch (error) {
      console.error(`Erro ao carregar ${type}:`, error);
    }
  };

  // 🆕 PROCESSAR DADOS DO GRAPHQL E MAPEAR CORRETAMENTE
  useEffect(() => {
    if (graphqlInfluencers && graphqlInfluencers.length > 0) {
      console.log('🔄 Processando dados do GraphQL...', {
        total: graphqlInfluencers.length,
        sample: graphqlInfluencers[0]
      });

      // Mapear dados do GraphQL usando a nova função de mapeamento
      const mappedInfluencers = graphqlInfluencers.map(gqlData => mapGraphQLDataToInfluencer(gqlData));
      
      // Salvar no estado de influenciadores
      setInfluencers(mappedInfluencers);
      
      // Salvar também no cache de dados da proposta para access mais rápido
      const proposalInfluencersMap: { [key: string]: Influencer } = {};
      mappedInfluencers.forEach(inf => {
        proposalInfluencersMap[inf.id] = inf;
      });
      setProposalInfluencersData(proposalInfluencersMap);

      console.log('✅ Dados do GraphQL processados e salvos:', {
        totalInfluencers: mappedInfluencers.length,
        sampleViews: {
          instagramStoriesViews: mappedInfluencers[0]?.instagramStoriesViews,
          instagramReelsViews: mappedInfluencers[0]?.instagramReelsViews,
          tiktokVideoViews: mappedInfluencers[0]?.tiktokVideoViews,
          youtubeShortsViews: mappedInfluencers[0]?.youtubeShortsViews,
          facebookViews: mappedInfluencers[0]?.facebookViews
        }
      });
    }
  }, [graphqlInfluencers]);

  // Debug: monitorar mudanças no estado dos influenciadores
  useEffect(() => {
    console.log('🎯 Estado dos influenciadores alterado:', {
      total: influencers.length,
      primeiro: influencers[0]?.nome,
      loading: isLoading
    });
  }, [influencers, isLoading]);

  // Adapter para converter PropostaPerfil[] para o formato da DataTable
  const adaptPropostaPerfisForTable = useMemo(() => {
    if (!proposta?.perfis) return [];
    
    return proposta.perfis.map(perfil => {
      // Obter dados detalhados do influenciador
      const influencerData = getInfluencerData(perfil.influencerId);
      
      // 🔧 CORRIGIR DADOS FUNDAMENTAIS PARA ORDENAÇÃO
      const seguidoresInstagram = influencerData?.instagramFollowers || influencerData?.redesSociais?.instagram?.seguidores || 0;
      const seguidoresTiktok = influencerData?.tiktokFollowers || influencerData?.redesSociais?.tiktok?.seguidores || 0;
      const seguidoresYoutube = influencerData?.youtubeFollowers || influencerData?.youtubeSubscribers || influencerData?.redesSociais?.youtube?.seguidores || 0;
      const seguidoresFacebook = influencerData?.facebookFollowers || 0;
      
      const maxSeguidores = Math.max(seguidoresInstagram, seguidoresTiktok, seguidoresYoutube, seguidoresFacebook);
      
      return {
        id: perfil.id,
        nome: perfil.nomeInfluencer,
        nomeInfluencer: perfil.nomeInfluencer,
        username: perfil.username || influencerData?.instagramUsername || influencerData?.youtubeUsername || influencerData?.tiktokUsername || '',
        plataforma: perfil.plataforma,
        seguidores: maxSeguidores || perfil.seguidores || 0,
        // Mapear status para tipos compatíveis
        status: (perfil.status === 'aceito' || perfil.status === 'rejeitado') ? 'ativo' : 
                (perfil.status === 'descartado') ? 'inativo' : 'pendente' as 'ativo' | 'inativo' | 'pendente',
        comentarios: perfil.comentarios,
        influencerId: perfil.influencerId,
        // Status original da proposta para uso nas colunas
        proposta_status: perfil.status,
        // Incluir dados financeiros se disponíveis
        dadosFinanceiros: influencerData?.dadosFinanceiros || (influencerData as any)?.financialData || (influencerData as any)?.financials,
        // Incluir orçamentos do estado local
        budgets: profileBudgets[perfil.id] || {},
        // Campos obrigatórios para compatibilidade com Influenciador
        engajamento: influencerData?.engagementRate || 0,
        
        // ✅ CAMPOS PARA ORDENAÇÃO GARANTINDO DADOS VÁLIDOS
        // Dados básicos do influenciador
        country: influencerData?.country || influencerData?.pais || 'N/A',
        location: influencerData?.location || 
                 (influencerData?.cidade && influencerData?.estado ? `${influencerData.cidade}/${influencerData.estado}` : 
                  influencerData?.cidade || influencerData?.estado || 'N/A'),
        age: Number(influencerData?.age || influencerData?.idade || 0),
        gender: influencerData?.gender || influencerData?.genero || 'N/A',
        category: influencerData?.category || influencerData?.categoria || 'N/A',
        verified: Boolean(influencerData?.isVerified || influencerData?.verificado || influencerData?.verified),
        mainNetwork: influencerData?.mainNetwork || 
                    (seguidoresInstagram >= Math.max(seguidoresTiktok, seguidoresYoutube) ? 'instagram' :
                     seguidoresTiktok >= seguidoresYoutube ? 'tiktok' : 'youtube'),
        engagementRate: Number(influencerData?.engagementRate || 0),
        promotesTraders: Boolean(influencerData?.promotesTraders ?? influencerData?.divulgaTrader ?? false),
        
        // Dados de redes sociais com fallbacks
        instagram_followers: seguidoresInstagram,
        tiktok_followers: seguidoresTiktok,
        youtube_followers: seguidoresYoutube,
        facebook_followers: seguidoresFacebook,
        
        // Views específicas garantindo números
        instagramStoriesViews: Number(influencerData?.instagramStoriesViews || 0),
        instagramReelsViews: Number(influencerData?.instagramReelsViews || 0),
        tiktokVideoViews: Number(influencerData?.tiktokVideoViews || 0),
        youtubeShortsViews: Number(influencerData?.youtubeShortsViews || 0),
        youtubeLongFormViews: Number(influencerData?.youtubeLongFormViews || 0),
        facebookViews: Number(influencerData?.facebookViews || 0),
        facebookStoriesViews: Number(influencerData?.facebookStoriesViews || 0),
        facebookReelsViews: Number(influencerData?.facebookReelsViews || 0),
        twitchViews: Number(influencerData?.twitchViews || 0),
        kwaiViews: Number(influencerData?.kwaiViews || 0),
        
        // Views gerais (para compatibilidade)
        tiktok_views: Number(influencerData?.tiktokVideoViews || influencerData?.socialNetworks?.tiktok?.avgViews || influencerData?.redesSociais?.tiktok?.curtidas || 0),
        youtube_views: Number(influencerData?.socialNetworks?.youtube?.avgViews || influencerData?.redesSociais?.youtube?.visualizacoes || 0),
        
        // Dados de contato e responsável
        responsibleInfo: influencerData?.responsibleName || influencerData?.agencyName || 'N/A',
        contato: influencerData?.whatsapp || influencerData?.email || 'N/A',
        
        // Serviços e orçamentos (para ordenação) - garantindo números válidos
        instagram_story: Number(profileBudgets[perfil.id]?.instagram_story || 0),
        instagram_reel: Number(profileBudgets[perfil.id]?.instagram_reel || 0),
        youtube_insertion: Number(profileBudgets[perfil.id]?.youtube_insertion || 0),
        youtube_video: Number(profileBudgets[perfil.id]?.youtube_video || 0),
        youtube_shorts: Number(profileBudgets[perfil.id]?.youtube_shorts || 0),
        tiktok_video: Number(profileBudgets[perfil.id]?.tiktok_video || 0),
        facebook_post: Number(profileBudgets[perfil.id]?.facebook_post || 0),
        twitch_stream: Number(profileBudgets[perfil.id]?.twitch_stream || 0),
        kwai_video: Number(profileBudgets[perfil.id]?.kwai_video || 0),
        total_budget: Object.values(profileBudgets[perfil.id] || {}).reduce((sum: number, value: number) => sum + (Number(value) || 0), 0),
        
        // Campos extras para compatibilidade com colunas existentes
        phone: influencerData?.phone || influencerData?.whatsapp || '',
        whatsapp: influencerData?.whatsapp || '',
        email: influencerData?.email || '',
        isAvailable: Boolean(influencerData?.isAvailable),
        rating: Number(influencerData?.rating || 0),
        state: influencerData?.state || influencerData?.estado || '',
        city: influencerData?.city || influencerData?.cidade || '',
        totalFollowers: Number(influencerData?.totalFollowers || maxSeguidores),
        
        // Campos de redes sociais específicos  
        instagramUsername: influencerData?.instagramUsername || influencerData?.redesSociais?.instagram?.username || '',
        instagramFollowers: seguidoresInstagram,
        instagramEngagementRate: Number(influencerData?.instagramEngagementRate || 0),
        instagramAvgViews: Number(influencerData?.instagramAvgViews || 0),
        
        tiktokUsername: influencerData?.tiktokUsername || influencerData?.redesSociais?.tiktok?.username || '',
        tiktokFollowers: seguidoresTiktok,
        tiktokEngagementRate: Number(influencerData?.tiktokEngagementRate || 0),
        tiktokAvgViews: Number(influencerData?.tiktokAvgViews || 0),
        
        youtubeUsername: influencerData?.youtubeUsername || influencerData?.redesSociais?.youtube?.username || '',
        youtubeFollowers: seguidoresYoutube,
        youtubeSubscribers: seguidoresYoutube,
        youtubeEngagementRate: Number(influencerData?.youtubeEngagementRate || 0),
        youtubeAvgViews: Number(influencerData?.youtubeAvgViews || 0),
        
        facebookUsername: influencerData?.facebookUsername || '',
        facebookFollowers: seguidoresFacebook,
        facebookEngagementRate: Number(influencerData?.facebookEngagementRate || 0),
        facebookAvgViews: Number(influencerData?.facebookAvgViews || 0),
        
        twitchUsername: influencerData?.twitchUsername || '',
        twitchFollowers: Number(influencerData?.twitchFollowers || 0),
        twitchEngagementRate: Number(influencerData?.twitchEngagementRate || 0),
        
        kwaiUsername: influencerData?.kwaiUsername || '',
        kwaiFollowers: Number(influencerData?.kwaiFollowers || 0),
        kwaiEngagementRate: Number(influencerData?.kwaiEngagementRate || 0),
        
        // Dados profissionais
        responsibleName: influencerData?.responsibleName || '',
        agencyName: influencerData?.agencyName || '',
        responsibleCapturer: influencerData?.responsibleCapturer || '',
        
        // Datas
        createdAt: influencerData?.createdAt || perfil.dataResposta || new Date(),
        updatedAt: influencerData?.updatedAt || new Date(),
        
        // Campos de ordenação customizados
        _sortKey_followers: maxSeguidores,
        _sortKey_engagement: Number(influencerData?.engagementRate || 0),
        _sortKey_age: Number(influencerData?.age || influencerData?.idade || 0),
        _sortKey_verified: influencerData?.isVerified || influencerData?.verificado ? 1 : 0,
        _sortKey_promotesTraders: influencerData?.promotesTraders || influencerData?.divulgaTrader ? 1 : 0,
      };
    });
  }, [proposta?.perfis, profileBudgets, proposalInfluencersData]);

  // Memoizar o objeto rowSelection para evitar loops infinitos
  const rowSelection = useMemo(() => {
    return selectedProposalInfluencers.reduce((acc, id) => {
      const index = adaptPropostaPerfisForTable.findIndex(row => row.id === id);
      if (index !== -1) acc[index] = true;
      return acc;
    }, {} as Record<string, boolean>);
  }, [selectedProposalInfluencers, adaptPropostaPerfisForTable]);

  // Função para lidar com mudanças na seleção
  const handleRowSelectionChange = useCallback((selectedRows: Record<string, boolean>) => {
    const selectedIds = Object.keys(selectedRows)
      .filter(key => selectedRows[key])
      .map(index => adaptPropostaPerfisForTable[parseInt(index)]?.id)
      .filter(Boolean);
    setSelectedProposalInfluencers(selectedIds);
  }, [adaptPropostaPerfisForTable]);

  // Cleanup quando o componente for desmontado
  useEffect(() => {
    return () => {
      document.body.style.pointerEvents = '';
      document.body.style.overflow = '';
    };
  }, []);

  // Monitorar mudanças nos estados dos modais para garantir limpeza
  useEffect(() => {
    if (!showBrandSelectionDialog && !showAddInfluencersDialog) {
      // Garantir que não há overlay persistindo quando ambos os modais estão fechados
      setTimeout(() => {
        document.body.style.pointerEvents = '';
        document.body.style.overflow = '';
        document.body.classList.remove('overflow-hidden');
        
        // Remover qualquer elemento de overlay órfão
        const overlays = document.querySelectorAll('[data-radix-dialog-overlay]');
        overlays.forEach(overlay => {
          if (!overlay.closest('[data-state="open"]')) {
            overlay.remove();
          }
        });
      }, 200);
    }
  }, [showBrandSelectionDialog, showAddInfluencersDialog]);

  // Função de chat removida

  // Função utilitária para limpar estados dos modais
  const clearModalStates = () => {
    // Limpar estilos do body
    document.body.style.pointerEvents = '';
    document.body.style.overflow = '';
    document.body.classList.remove('overflow-hidden');
    
    // Remover qualquer overlay que possa estar persistindo
    const overlays = document.querySelectorAll('[data-radix-popper-content-wrapper]');
    overlays.forEach(overlay => overlay.remove());
    
    const backdropElements = document.querySelectorAll('[data-state="open"]');
    backdropElements.forEach(element => {
      if (element.getAttribute('data-radix-dialog-overlay')) {
        element.remove();
      }
    });
    
    // Limpar estados
    setSelectedInfluencers([]);
    setSelectedBrandForInfluencers(null);
    setSearchTerm('');
    
    // Forçar re-render
    setTimeout(() => {
      document.body.style.pointerEvents = '';
      document.body.style.overflow = '';
    }, 100);
  };

  // 🔥 IMPLEMENTAR FUNÇÃO PARA CARREGAR INFLUENCIADORES VIA GRAPHQL
  const loadAllInfluencers = async () => {
    if (!currentUser?.id) {
      console.error('❌ Usuário não autenticado');
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive"
      });
      return;
    }

    try {
      console.log('🔄 Carregando influenciadores via GraphQL...');
      showLoader();
      
      // Buscar influenciadores usando o hook GraphQL
      // Os dados serão sincronizados automaticamente via useEffect
      await refetchInfluencers();
      
      console.log('✅ Carregamento via GraphQL iniciado, aguardando processamento...');
      
    } catch (error) {
      console.error('❌ Erro ao carregar influenciadores via GraphQL:', error);
      toast({
        title: "Erro", 
        description: "Erro ao carregar influenciadores. Tente novamente.",
        variant: "destructive"
      });
      setInfluencers([]);
    } finally {
      // O loader será escondido quando os dados forem processados no useEffect
      setTimeout(() => hideLoader(), 1000);
    }
  };

    const loadMultipleInfluencersData = async (influencerIds: string[]): Promise<{ [key: string]: Influencer }> => {
    try {
      if (influencerIds.length === 0) return {};
      
      console.log('🚀 [REST] Carregando influenciadores via API REST...', {
        totalIds: influencerIds.length,
        timestamp: new Date().toISOString()
      });
      
      const influencersData: { [key: string]: Influencer } = {};
      
      // Carregar em batches para não sobrecarregar a API
      const BATCH_SIZE = 5;
      
      for (let i = 0; i < influencerIds.length; i += BATCH_SIZE) {
        const batch = influencerIds.slice(i, i + BATCH_SIZE);
        
        const batchPromises = batch.map(async (influencerId) => {
          try {
            const response = await fetch(`/api/influencers/${influencerId}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              console.warn(`⚠️ [REST] Erro ao carregar influenciador ${influencerId}: ${response.status}`);
              return null;
            }

            const influencerData = await response.json();
            
            if (influencerData) {
              console.log(`✅ [REST] Carregado: ${influencerData.nome || influencerData.name}`);
              
              // Converter para formato esperado
              const influencer: Influencer = {
                id: influencerData.id,
                nome: influencerData.nome || influencerData.name || '',
                verificado: influencerData.verificado || influencerData.isVerified || false,
                verified: influencerData.verificado || influencerData.isVerified || false,
                categoria: influencerData.categoria || influencerData.category || '',
                pais: influencerData.pais || influencerData.country || '',
                cidade: influencerData.cidade || influencerData.city || '',
                estado: influencerData.estado || influencerData.state || '',
                idade: influencerData.idade || influencerData.age || 0,
                divulgaTrader: influencerData.divulgaTrader || influencerData.promotesTraders || false,
                genero: influencerData.genero || influencerData.gender || 'Outro',
                // ✅ Campos removidos do snapshot (não usar fallbacks)
                whatsapp: influencerData.whatsapp || '',
                avatar: influencerData.avatar || '',
                email: influencerData.email || '',
                servicos: {
                  postFeed: 0,
                  stories: 0,
                  reels: 0,
                  videoYoutube: 0,
                  videoTiktok: 0
                },
                // Dados financeiros
                dadosFinanceiros: influencerData.dadosFinanceiros || influencerData.pricing,
                // Dados demográficos
                audienceGender: influencerData.audienceGender,
                audienceAgeRanges: influencerData.audienceAgeRanges,
                // Redes sociais
                redesSociais: influencerData.redesSociais || {
                  instagram: influencerData.instagramUsername ? {
                    username: influencerData.instagramUsername,
                    seguidores: influencerData.instagramFollowers || 0,
                    engajamento: influencerData.instagramEngagementRate || 0
                  } : undefined,
                  youtube: influencerData.youtubeUsername ? {
                    username: influencerData.youtubeUsername,
                    seguidores: influencerData.youtubeFollowers || 0,
                    visualizacoes: influencerData.youtubeAvgViews || 0
                  } : undefined,
                  tiktok: influencerData.tiktokUsername ? {
                    username: influencerData.tiktokUsername,
                    seguidores: influencerData.tiktokFollowers || 0,
                    curtidas: influencerData.tiktokAvgViews || 0
                  } : undefined
                },
                // Campos adicionais
                country: influencerData.country,
                age: influencerData.age,
                gender: influencerData.gender,
                category: influencerData.category,
                isVerified: influencerData.isVerified,
                location: influencerData.location,
                phone: influencerData.phone,
                mainNetwork: influencerData.mainNetwork || 'instagram',
                promotesTraders: influencerData.promotesTraders,
                responsibleName: influencerData.responsibleName,
                agencyName: influencerData.agencyName,
                engagementRate: influencerData.engagementRate || 0,
                // 🔑 ADICIONAR CAMPOS ESPECÍFICOS DE VISUALIZAÇÕES
                instagramStoriesViews: influencerData.instagramStoriesViews,
                instagramReelsViews: influencerData.instagramReelsViews,
                tiktokVideoViews: influencerData.tiktokVideoViews,
                youtubeShortsViews: influencerData.youtubeShortsViews,
                youtubeLongFormViews: influencerData.youtubeLongFormViews,
                facebookViews: influencerData.facebookViews,
                facebookStoriesViews: influencerData.facebookStoriesViews,
                facebookReelsViews: influencerData.facebookReelsViews,
                twitchViews: influencerData.twitchViews,
                kwaiViews: influencerData.kwaiViews
              };

              return { id: influencerId, data: influencer };
            }
            return null;
          } catch (error) {
            console.error(`❌ [REST] Erro ao carregar ${influencerId}:`, error);
            return null;
          }
        });
        
        const batchResults = await Promise.all(batchPromises);
        
        batchResults.forEach(result => {
          if (result && result.data) {
            influencersData[result.id] = result.data;
          }
        });
        
        console.log(`✅ [REST] Batch ${Math.ceil((i + BATCH_SIZE) / BATCH_SIZE)} concluído: ${Object.keys(influencersData).length} total carregados`);
      }
      
      console.log(`✅ [REST] Carregamento concluído: ${Object.keys(influencersData).length} influenciadores`);
      return influencersData;
      
    } catch (error) {
      console.error('❌ [CRITICAL] Erro crítico no carregamento de influenciadores:', error);
      return {};
    }
  };

    // Função para carregar influenciadores via GraphQL (movida para cima para evitar problema de escopo)
  const loadInfluencersViaGraphQL = async (influencerIds: string[]): Promise<{ [key: string]: Influencer }> => {
    try {
      if (influencerIds.length === 0) return {};
      
      // 🔒 VERIFICAÇÃO CRÍTICA: Garantir que o usuário está autenticado
      if (!currentUser?.id) {
        console.warn('⚠️ [GraphQL] Usuário não autenticado - aguardando login...');
        return {};
      }
      
      console.log('🚀 [GraphQL] Carregando influenciadores via GraphQL...', {
        totalIds: influencerIds.length,
        userId: currentUser.id,
        timestamp: new Date().toISOString()
      });
      
      // ✅ CORREÇÃO: Usar query otimizada para buscar TODOS de uma vez
      const influencersData: { [key: string]: Influencer } = {};
      
      // 🔑 Obter token de autenticação do Clerk
      const idToken = await getToken();
      console.log('🔑 [GraphQL] Token obtido:', !!idToken);
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Adicionar token se disponível
      if (idToken) {
        headers['Authorization'] = `Bearer ${idToken}`;
      }
      
      // ✅ UMA ÚNICA QUERY para buscar TODOS os influenciadores
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          query: `
            query GetInfluencersByIds($ids: [ID!]!, $userId: ID!, $proposalId: ID) {
              influencersByIds(ids: $ids, userId: $userId, proposalId: $proposalId) {
                influencers {
                  id
                  name
                  email
                  phone
                  whatsapp
                  avatar
                  bio
                  totalFollowers
                  engagementRate
                  rating
                  isVerified
                  isAvailable
                  status
                  category
                  categories
                  country
                  state
                  city
                  location
                  age
                  gender
                  
                  instagramUsername
                  instagramFollowers
                  instagramEngagementRate
                  instagramAvgViews
                  instagramStoriesViews
                  instagramReelsViews
                  
                  tiktokUsername
                  tiktokFollowers
                  tiktokEngagementRate
                  tiktokAvgViews
                  tiktokVideoViews
                  
                  youtubeUsername
                  youtubeFollowers
                  youtubeSubscribers
                  youtubeEngagementRate
                  youtubeAvgViews
                  youtubeShortsViews
                  youtubeLongFormViews
                  
                  facebookUsername
                  facebookFollowers
                  facebookEngagementRate
                  facebookAvgViews
                  facebookViews
                  facebookStoriesViews
                  facebookReelsViews
                  
                  twitchUsername
                  twitchFollowers
                  twitchEngagementRate
                  twitchViews
                  
                  kwaiUsername
                  kwaiFollowers
                  kwaiEngagementRate
                  kwaiViews
                  
                  promotesTraders
                  responsibleName
                  agencyName
                  responsibleCapturer
                  
                  currentPricing {
                    id
                    services {
                      instagram {
                        story { price currency }
                        reel { price currency }
                        post { price currency }
                      }
                      youtube {
                        insertion { price currency }
                        dedicated { price currency }
                        shorts { price currency }
                      }
                      tiktok {
                        video { price currency }
                      }
                      facebook {
                        post { price currency }
                        story { price currency }
                      }
                      twitch {
                        stream { price currency }
                      }
                      kwai {
                        video { price currency }
                      }
                    }
                    isActive
                    validFrom
                    validUntil
                  }
                  
                  currentDemographics {
                    id
                    platform
                    audienceGender {
                      male
                      female
                      other
                    }
                    audienceLocations {
                      country
                      percentage
                    }
                    audienceCities {
                      city
                      percentage
                    }
                    audienceAgeRange {
                      range
                      percentage
                    }
                    isActive
                    source
                  }
                  
                  createdAt
                  updatedAt
                }
                foundIds
                notFoundIds
                totalFound
                totalRequested
                processingTimeMs
                hasPartialFailure
                errors
              }
            }
          `,
          variables: {
            ids: influencerIds,
            userId: currentUser.id, // Agora garantido que existe pela verificação acima
            proposalId: propostaId
          }
        })
      });

      if (!response.ok) {
        console.warn(`❌ [GraphQL] Erro na resposta: ${response.status}`);
        return {};
      }

      const result = await response.json();
      
      if (result.errors) {
        console.error(`❌ [GraphQL] Erros GraphQL:`, result.errors);
        return {};
      }

      const influencersResult = result.data?.influencersByIds;
      
      if (!influencersResult) {
        console.warn(`⚠️ [GraphQL] Resultado vazio ou inválido`);
        return {};
      }

      // 🎯 PROCESSAR RESULTADOS - Uma única vez para todos
      const foundInfluencers = influencersResult.influencers || [];
      
      console.log(`📊 [GraphQL] Resultado da busca:`, {
        totalRequested: influencersResult.totalRequested,
        totalFound: influencersResult.totalFound,
        foundInfluencers: foundInfluencers.length,
        notFoundIds: influencersResult.notFoundIds?.length || 0,
        processingTimeMs: influencersResult.processingTimeMs
      });

      // Converter cada influenciador para o formato esperado
      for (const gqlInfluencer of foundInfluencers) {
        const influencer: Influencer = {
          id: gqlInfluencer.id,
          nome: gqlInfluencer.name,
          email: gqlInfluencer.email,
          verificado: gqlInfluencer.isVerified,
          verified: gqlInfluencer.isVerified,
          categoria: gqlInfluencer.category,
          pais: gqlInfluencer.country,
          cidade: gqlInfluencer.city,
          estado: gqlInfluencer.state,
          idade: gqlInfluencer.age,
          divulgaTrader: gqlInfluencer.promotesTraders,
          genero: gqlInfluencer.gender,
          whatsapp: gqlInfluencer.whatsapp,
          avatar: gqlInfluencer.avatar,

          // bio: gqlInfluencer.bio, // Temporariamente comentado devido ao erro de linter
          engagement: gqlInfluencer.engagementRate,
          rating: gqlInfluencer.rating,
          totalFollowers: gqlInfluencer.totalFollowers,
          engagementRate: gqlInfluencer.engagementRate,
          location: gqlInfluencer.location || `${gqlInfluencer.city || ''}, ${gqlInfluencer.state || ''}, ${gqlInfluencer.country || ''}`.replace(/^,\s*|,\s*$/g, ''),
          mainNetwork: gqlInfluencer.instagramUsername ? 'instagram' : 
                      gqlInfluencer.youtubeUsername ? 'youtube' : 
                      gqlInfluencer.tiktokUsername ? 'tiktok' : 'instagram',
          promotesTraders: gqlInfluencer.promotesTraders,
          responsibleName: gqlInfluencer.responsibleName,
          agencyName: gqlInfluencer.agencyName,
          
          // 🔥 MAPEAMENTO COMPLETO DOS CAMPOS DE VIEWS E REDES SOCIAIS
          // Dados de redes sociais estruturados
          redesSociais: {
            instagram: gqlInfluencer.instagramUsername ? {
              username: gqlInfluencer.instagramUsername,
              seguidores: gqlInfluencer.instagramFollowers || 0,
              engajamento: gqlInfluencer.instagramEngagementRate || 0
            } : undefined,
            youtube: gqlInfluencer.youtubeUsername ? {
              username: gqlInfluencer.youtubeUsername,
              seguidores: gqlInfluencer.youtubeFollowers || 0,
              visualizacoes: gqlInfluencer.youtubeAvgViews || 0
            } : undefined,
            tiktok: gqlInfluencer.tiktokUsername ? {
              username: gqlInfluencer.tiktokUsername,
              seguidores: gqlInfluencer.tiktokFollowers || 0,
              curtidas: gqlInfluencer.tiktokAvgViews || 0
            } : undefined
          },
          
          // 🎯 CAMPOS DE VIEWS ESPECÍFICOS MAPEADOS CORRETAMENTE
          instagramStoriesViews: gqlInfluencer.instagramStoriesViews || 0,
          instagramReelsViews: gqlInfluencer.instagramReelsViews || 0,
          tiktokVideoViews: gqlInfluencer.tiktokVideoViews || 0,
          youtubeShortsViews: gqlInfluencer.youtubeShortsViews || 0,
          youtubeLongFormViews: gqlInfluencer.youtubeLongFormViews || 0,
          facebookViews: gqlInfluencer.facebookViews || 0,
          facebookReelsViews: gqlInfluencer.facebookReelsViews || 0,
          facebookStoriesViews: gqlInfluencer.facebookStoriesViews || 0,
          twitchViews: gqlInfluencer.twitchViews || 0,
          kwaiViews: gqlInfluencer.kwaiViews || 0,
          
          // Campos alternativos para compatibilidade
          stories_views: gqlInfluencer.instagramStoriesViews || 0,
          instagram_reels_views: gqlInfluencer.instagramReelsViews || 0,
          youtube_insertion_views: gqlInfluencer.youtubeAvgViews || 0,
          youtube_long_video_views: gqlInfluencer.youtubeLongFormViews || 0,
          youtube_shorts_views: gqlInfluencer.youtubeShortsViews || 0,
          tiktok_views: gqlInfluencer.tiktokVideoViews || 0,
          
          // Dados de pricing do GraphQL
          currentPricing: gqlInfluencer.currentPricing,
          // Dados demográficos do GraphQL  
          audienceGender: gqlInfluencer.currentDemographics?.[0]?.audienceGender,
          audienceAgeRanges: gqlInfluencer.currentDemographics?.[0]?.audienceAgeRange ? {
            [gqlInfluencer.currentDemographics[0].platform || 'instagram']: gqlInfluencer.currentDemographics[0].audienceAgeRange
          } : undefined,
          audienceLocations: gqlInfluencer.currentDemographics?.[0]?.audienceLocations,
          audienceCities: gqlInfluencer.currentDemographics?.[0]?.audienceCities,
          
          // Campos de serviços básicos para compatibilidade
          servicos: {
            postFeed: 0,
            stories: 0,
            reels: 0,
            videoYoutube: 0,
            videoTiktok: 0
          }
        };

        influencersData[gqlInfluencer.id] = influencer;
        console.log(`✅ [GraphQL] Processado: ${influencer.nome}`);
      }
      
      console.log(`✅ [GraphQL] Carregamento concluído: ${Object.keys(influencersData).length} influenciadores em UMA query`);
      return influencersData;
      
    } catch (error) {
      console.error('❌ [GraphQL] Erro geral ao carregar influenciadores:', error);
      return {};
    }
  };

  // ✅ FUNÇÃO MELHORADA: Carregamento com retry e melhor cache
  const loadProposta = async (retryCount = 0, skipLoader = false) => {
    const startTime = performance.now();
    const MAX_RETRIES = 2;

    try {
      if (!skipLoader) {
        showLoader("");
      }

      console.log('🔍 Carregando proposta:', propostaId, retryCount > 0 ? `(tentativa ${retryCount + 1})` : '');
      
      if (!propostaId) {
        console.error('❌ ID da proposta não encontrado');
        return;
      }

      // Buscar proposta do ProposalService
      const proposalData = await ProposalService.getProposalById(propostaId);
      
      if (!proposalData) {
        console.error('❌ Proposta não encontrada');
        setProposta(null);
        return;
      }

      console.log('✅ Proposta carregada:', proposalData);

      // ✨ IMPLEMENTAÇÃO MELHORADA: Buscar influenciadores da subcoleção com retry
      console.log('📋 Buscando influenciadores da subcoleção...');
      
      let subcollectionInfluencers = await ProposalService.getProposalInfluencers(propostaId);
      
      // Se não encontrou influenciadores e é uma tentativa, espera um pouco e tenta novamente
      if (subcollectionInfluencers.length === 0 && retryCount < MAX_RETRIES) {
        console.log('⏳ Aguardando sincronização do Firebase...');
        await new Promise(resolve => setTimeout(resolve, 1500)); // Aguarda 1.5s
        subcollectionInfluencers = await ProposalService.getProposalInfluencers(propostaId);
      }
      
      console.log('👥 Influenciadores da subcoleção:', subcollectionInfluencers);

      let influencersData: { [key: string]: Influencer } = {};
      let perfisFormatados: PropostaPerfil[] = [];

      if (subcollectionInfluencers.length > 0) {
        // Extrair IDs dos influenciadores da subcoleção
        const influencerIds = subcollectionInfluencers.map(inf => inf.influencerId);
        console.log('🔍 IDs dos influenciadores para buscar:', influencerIds);

        // ✅ CARREGAR DADOS COM ESTRATÉGIA DE CACHE INTELIGENTE
        console.log('📊 Carregando dados dos influenciadores via GraphQL...');
        
        // Verificar se temos alguns dados em cache válido
        const now = Date.now();
        const cachedData: { [key: string]: Influencer } = {};
        const uncachedIds: string[] = [];
        
        influencerIds.forEach(id => {
          const cached = influencerCache.current[id];
          if (cached && (now - cached.timestamp) < CACHE_DURATION) {
            cachedData[id] = cached.data;
            console.log(`📦 Usando cache para ${id}`);
          } else {
            uncachedIds.push(id);
          }
        });
        
        // Carregar apenas os não cacheados
        let newData: { [key: string]: Influencer } = {};
        if (uncachedIds.length > 0) {
          console.log(`🌐 Buscando ${uncachedIds.length} influenciadores via GraphQL...`);
          
          // 🔒 VERIFICAÇÃO: Só carregar se o usuário estiver autenticado
          if (currentUser?.id) {
            newData = await loadInfluencersViaGraphQL(uncachedIds);
            
            // Atualizar cache
            Object.entries(newData).forEach(([id, data]) => {
              influencerCache.current[id] = { data, timestamp: now };
            });
          } else {
            console.warn('⚠️ Usuário não autenticado - pulando carregamento via GraphQL');
          }
        }
        
        // Combinar dados do cache e novos dados
        influencersData = { ...cachedData, ...newData };
        console.log('✅ Dados dos influenciadores carregados:', Object.keys(influencersData).length);

        // ✅ CRIAR PERFIS FORMATADOS COM DADOS MAIS ROBUSTOS
        perfisFormatados = subcollectionInfluencers.map((subcollectionInfluencer: any) => {
          const influencerData = influencersData[subcollectionInfluencer.influencerId];
          
          // Garantir que temos pelo menos dados básicos
          const nomeInfluencer = influencerData?.nome || 
                                (influencerData as any)?.name || 
                                `Influenciador ${subcollectionInfluencer.influencerId.substring(0, 8)}`;
          
          const username = influencerData?.redesSociais?.instagram?.username || 
                          influencerData?.redesSociais?.youtube?.username || 
                          influencerData?.redesSociais?.tiktok?.username ||
                          (influencerData as any)?.instagramUsername ||
                          (influencerData as any)?.youtubeUsername ||
                          (influencerData as any)?.tiktokUsername || 'N/A';
          
          const plataforma = influencerData?.mainNetwork || 
                            (influencerData?.redesSociais?.instagram ? 'instagram' : 
                             influencerData?.redesSociais?.youtube ? 'youtube' : 
                             influencerData?.redesSociais?.tiktok ? 'tiktok' : 'instagram');
          
          const seguidores = Math.max(
            influencerData?.redesSociais?.instagram?.seguidores || 0,
            influencerData?.redesSociais?.youtube?.seguidores || 0,
            influencerData?.redesSociais?.tiktok?.seguidores || 0,
            (influencerData as any)?.instagramFollowers || 0,
            (influencerData as any)?.youtubeFollowers || 0,
            (influencerData as any)?.tiktokFollowers || 0
          );
          
          return {
            id: `${propostaId}_${subcollectionInfluencer.influencerId}`,
            influencerId: subcollectionInfluencer.influencerId,
            nomeInfluencer,
            username,
            plataforma,
            seguidores,
            status: subcollectionInfluencer.status || 'pendente',
            comentarios: subcollectionInfluencer.comments || '',
            valor: 0,
            dataResposta: subcollectionInfluencer.updatedAt ? new Date(subcollectionInfluencer.updatedAt) : undefined
          };
        });

        console.log('✅ Perfis formatados criados:', perfisFormatados.length);
        
        // ✅ VALIDAÇÃO: Se ainda temos perfis com dados incompletos, fazer retry
        const perfisComDadosIncompletos = perfisFormatados.filter(perfil => 
          perfil.nomeInfluencer.includes('Influenciador ') || 
          perfil.seguidores === 0
        ).length;
        
        if (perfisComDadosIncompletos > 0 && retryCount < MAX_RETRIES) {
          console.log(`⚠️ Encontrados ${perfisComDadosIncompletos} perfis com dados incompletos. Fazendo retry...`);
          // Limpar cache dos problemáticos
          subcollectionInfluencers.forEach(inf => {
            delete influencerCache.current[inf.influencerId];
          });
          
          return loadProposta(retryCount + 1, skipLoader);
        }
        
      } else {
        console.log('⚠️ Nenhum influenciador encontrado na subcoleção');
        
        // Fallback: tentar usar estrutura antiga se existir
        const influencerIds = proposalData.influencers?.map((inf: any) => inf.id) || [];
        
        if (influencerIds.length > 0) {
          console.log('🔄 Usando estrutura antiga como fallback:', influencerIds);
          influencersData = await loadMultipleInfluencersData(influencerIds);
          
          perfisFormatados = proposalData.influencers?.map((proposalInfluencer: any) => {
            const influencerData = influencersData[proposalInfluencer.id];
            
            return {
              id: `${propostaId}_${proposalInfluencer.id}`,
              influencerId: proposalInfluencer.id,
              nomeInfluencer: influencerData?.nome || proposalInfluencer.name || 'Nome não encontrado',
              username: influencerData?.redesSociais?.instagram?.username || 
                       influencerData?.redesSociais?.youtube?.username || 
                       influencerData?.redesSociais?.tiktok?.username || 'N/A',
              plataforma: influencerData?.mainNetwork || 'N/A',
              seguidores: (influencerData?.redesSociais?.instagram?.seguidores || 
                           influencerData?.redesSociais?.youtube?.seguidores || 
                           influencerData?.redesSociais?.tiktok?.seguidores || 0),
              status: proposalInfluencer.status || 'pendente',
              comentarios: proposalInfluencer.comments || '',
              valor: 0,
              dataResposta: proposalInfluencer.updatedAt ? new Date(proposalInfluencer.updatedAt) : undefined
            };
          }) || [];
        }
      }

      const propostaFormatada: Proposta = {
        id: proposalData.id,
        nome: proposalData.nome || 'Proposta sem nome',
        descricao: proposalData.descricao,
        criadoPor: proposalData.criadoPor || '',
        brandId: proposalData.brandId || '',
        grupo: proposalData.grupo,
        priority: proposalData.priority || 'medium',
        status: proposalData.status || 'draft',
        influencers: subcollectionInfluencers.length > 0 ? 
          subcollectionInfluencers.map((inf: any) => ({ id: inf.influencerId, name: influencersData[inf.influencerId]?.nome || 'N/A' })) :
          proposalData.influencers || [],
        totalAmount: proposalData.totalAmount || 0,
        dataCriacao: proposalData.createdAt ? new Date(proposalData.createdAt) : new Date(),
        ultimaAtualizacao: proposalData.updatedAt ? new Date(proposalData.updatedAt) : new Date(),
        dataEnvio: proposalData.dataEnvio,
        services: proposalData.services || [],
        perfis: perfisFormatados,
        criador: proposalData.criadoPor || '',
        budgets: proposalData.budgets
      };

      console.log('🎯 Proposta formatada:', propostaFormatada);
      console.log('👥 Total de perfis carregados:', perfisFormatados.length);
      
      setProposta(propostaFormatada);
      setProposalInfluencersData(influencersData);

      recordMetric('loadProposta', performance.now() - startTime, {
        influencersCount: Object.keys(influencersData).length,
        perfisCount: perfisFormatados.length,
        usingSubcollection: subcollectionInfluencers.length > 0,
        retryCount
      });
      
    } catch (error) {
      console.error('❌ Erro ao carregar proposta:', error);
      
      // Em caso de erro, tentar retry se ainda há tentativas
      if (retryCount < MAX_RETRIES) {
        console.log(`🔄 Tentando novamente... (${retryCount + 1}/${MAX_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return loadProposta(retryCount + 1, skipLoader);
      }
      
      setProposta(null);
    } finally {
      if (!skipLoader) {
        hideLoader();
      }
    }
  };

  const handleStatusChange = async (perfilId: string, newStatus: 'aceito' | 'rejeitado' | 'pendente') => {
    if (!proposta || !currentUser) return;
    
    try {
      showLoader("");
      
      // Encontrar o perfil para obter o influencerId
      const perfil = proposta.perfis.find(p => p.id === perfilId);
      if (!perfil) return;
      
      // Atualizar status na subcoleção do Firebase
      await ProposalService.updateInfluencerStatus(
        proposta.id,
        perfil.influencerId,
        newStatus,
        currentUser.id
      );
      
      // ✨ OTIMIZAÇÃO: Atualizar apenas o status localmente sem recarregar toda a página
      console.log('🔄 Atualizando status do perfil localmente...');
      
      // Atualizar estado da proposta localmente
      setProposta(prev => {
        if (!prev) return prev;
        
        return {
          ...prev,
          perfis: prev.perfis.map(p => 
            p.id === perfilId 
              ? { ...p, status: newStatus, dataResposta: new Date() }
              : p
          ),
          influencers: prev.influencers.map(inf => 
            inf.id === perfil.influencerId
              ? { ...inf }
              : inf
          )
        };
      });

      console.log('✅ Status atualizado na tabela com sucesso!');
      
      const statusText = newStatus === 'aceito' ? 'aceito' : 
                        newStatus === 'rejeitado' ? 'recusado' : 'em reserva';
      
      toast({
        title: "Status atualizado",
        description: `Perfil marcado como ${statusText} e salvo.`,
      });
      
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o status do perfil.",
        variant: "destructive",
      });
    } finally {
      hideLoader();
    }
  };

  // ✅ FUNÇÃO UNIFICADA: Carregar orçamentos via API /api/budgets
  const loadBudgetsFromFirebase = async () => {
    console.log('📥 [UNIFICADO] Carregando orçamentos via API...');
    
    if (!proposta?.perfis || !currentUser) {
      console.log('⚠️ [UNIFICADO] Dados necessários não disponíveis:', {
        hasPerfis: !!proposta?.perfis,
        hasCurrentUser: !!currentUser
      });
      return;
    }

    try {
      const localBudgets: { [profileId: string]: { [service: string]: number } } = {};

      // ✅ USAR API UNIFICADA: Buscar orçamentos de todos os influenciadores da proposta
      console.log(`🌐 [UNIFICADO] Buscando orçamentos da proposta ${proposta.id} via API...`);
      
      const response = await fetch(`/api/budgets?userId=${currentUser.id}&proposalId=${proposta.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Erro na API: ${response.status}`);
      }

      const data = await response.json();
      console.log('💰 [UNIFICADO] Orçamentos recebidos da API:', data);

      if (data.success && data.budgets && Array.isArray(data.budgets)) {
        // Organizar orçamentos por perfil (profileId)
        data.budgets.forEach((budget: any) => {
          // Encontrar o perfil correspondente ao influencerId
          const perfil = proposta.perfis.find(p => p.influencerId === budget.influencerId);
          if (perfil) {
            if (!localBudgets[perfil.id]) {
              localBudgets[perfil.id] = {};
            }
            localBudgets[perfil.id][budget.serviceType] = budget.amount;
            console.log(`✅ [UNIFICADO] Orçamento mapeado: ${perfil.id} -> ${budget.serviceType} = ${budget.amount}`);
          }
        });
      }

      console.log('🎯 [UNIFICADO] Orçamentos organizados por perfil:', localBudgets);
      setProfileBudgets(localBudgets);

    } catch (error) {
      console.error('❌ [UNIFICADO] Erro ao carregar orçamentos via API:', error);
      setProfileBudgets({});
    }
  };

  // ✅ FUNÇÃO UNIFICADA: Usar API /api/budgets para sincronização total
  const saveBudgetToFirebase = async (profileId: string, service: string, budgetedPrice: number) => {
    console.log('💾 [UNIFICADO] Salvando orçamento via API:', { profileId, service, budgetedPrice });
    
    if (!proposta || !currentUser) {
      const error = 'Dados necessários não disponíveis';
      console.error('❌ Erro:', error, { 
        hasProposal: !!proposta, 
        hasCurrentUser: !!currentUser 
      });
      throw new Error(error);
    }

    try {
      // Obter dados do perfil
      const perfil = proposta.perfis.find(p => p.id === profileId);
      if (!perfil) {
        const error = 'Perfil não encontrado';
        console.error('❌ Erro:', error, { profileId, availableProfiles: proposta.perfis.map(p => p.id) });
        throw new Error(error);
      }

      console.log('👤 [UNIFICADO] Perfil encontrado:', perfil);

      // ✅ USAR API UNIFICADA: Criar orçamento via /api/budgets
      const budgetData = {
        influencerId: perfil.influencerId,
        influencerName: perfil.nomeInfluencer || 'Influenciador',
        userId: currentUser.id,
        brandId: proposta.brandId,
        proposalId: proposta.id, // ✅ GARANTIR QUE USA A ESTRUTURA PROPOSALSERVICE
        amount: budgetedPrice,
        currency: 'BRL',
        description: `Orçamento para ${service}`,
        serviceType: service
      };

      console.log('🌐 [UNIFICADO] Enviando para API /api/budgets:', budgetData);

      const response = await fetch('/api/budgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(budgetData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Erro na API: ${errorData.error || 'Erro desconhecido'}`);
      }

      const result = await response.json();
      console.log('✅ [UNIFICADO] Orçamento salvo via API:', result);

      // Recarregar dados da proposta
      await loadProposta();

      console.log('🔄 [UNIFICADO] Dados da proposta recarregados - sincronização completa!');

    } catch (error) {
      console.error('❌ [UNIFICADO] Erro ao salvar orçamento:', error);
      throw error;
    }
  };

  const handleEditBudget = async (profileId: string, service: string, currentValue: number) => {
    // Encontrar dados do influenciador para mostrar no dialog
    const perfil = proposta?.perfis.find(p => p.id === profileId);
    const influencerName = perfil?.nomeInfluencer || 'Influenciador';
    const serviceName = getServiceName(service);
    
    // Obter preço original do influenciador
    const influencerData = getInfluencerData(perfil?.influencerId || '');
    let originalPrice = 0;
    
    if (influencerData?.dadosFinanceiros) {
      const financialData = influencerData.dadosFinanceiros;
      
      // Mapear o service para o campo correto nos dados financeiros
      const serviceMapping: { [key: string]: string } = {
        'instagram_story': 'instagramStory',
        'instagram_reel': 'instagramReel', 
        'youtube_video': 'youtubeDedicated',
        'youtube_shorts': 'youtubeShorts',
        'tiktok_video': 'tiktokVideo'
      };
      
      const serviceKey = serviceMapping[service];
      if (serviceKey) {
        originalPrice = financialData.precos?.[serviceKey as keyof typeof financialData.precos]?.price || 
                      financialData.prices?.[serviceKey as keyof typeof financialData.prices]?.price || 0;
      }
    }
    
    // Obter quantidade atual do serviço
    const currentQuantity = serviceQuantities[profileId]?.[service] || 1;
    
    // Configurar dados do dialog
    setBudgetEditData({
      profileId,
      service,
      currentValue,
      influencerName,
      serviceName,
      originalPrice,
      currentQuantity
    });
    
    // Pré-preencher os campos com os valores atuais
    setBudgetInputValue(currentValue > 0 ? currentValue.toString() : '');
    setQuantityInputValue(currentQuantity.toString());
    
    // Abrir dialog
    setShowBudgetDialog(true);
  };

  // FUNÇÕES PARA GERENCIAR QUANTIDADES DOS SERVIÇOS

  const saveServiceQuantity = async (profileId: string, service: string, quantity: number, unitValue?: number) => {
    console.log('💾 Salvando quantidade do serviço:', { profileId, service, quantity });
    
    if (!proposta || !currentUser) {
      toast({
        title: "Erro",
        description: "Dados necessários não disponíveis",
        variant: "destructive",
      });
      return;
    }

    try {
      // Encontrar o perfil correspondente
      const perfil = proposta.perfis.find(p => p.id === profileId);
      if (!perfil) {
        throw new Error('Perfil não encontrado');
      }

      showLoader("Salvando quantidade...");

      // 🆕 IMPLEMENTAÇÃO CORRIGIDA: Buscar ou criar orçamento antes de adicionar quantidade
      const serviceKey = `${service.toLowerCase()}`;
      
      // Buscar o budget existente para este influenciador e serviço
      const budgetResponse = await fetch(`/api/budgets?influencerId=${perfil.influencerId}&proposalId=${proposta.id}&userId=${currentUser.id}`);
      
      if (!budgetResponse.ok) {
        throw new Error('Erro ao buscar orçamentos existentes');
      }
      
      const budgetData = await budgetResponse.json();
      let existingBudget = budgetData.budgets?.find((b: any) => b.serviceType === service || b.id === serviceKey);
      
      // 🔥 CORREÇÃO: Se não existe orçamento, CRIAR automaticamente
      if (!existingBudget) {
        console.log('🆕 Orçamento não existe, criando automaticamente...');
        
        // 🔐 VALIDAÇÃO: Verificar se todos os campos obrigatórios estão presentes
        if (!perfil.influencerId) {
          throw new Error('ID do influenciador não encontrado no perfil');
        }
        
        if (!currentUser?.id) {
          throw new Error('Usuário não autenticado');
        }
        
        if (!proposta.brandId) {
          throw new Error('ID da marca não encontrado na proposta');
        }
        
        // Obter preço original do influenciador (se disponível)
        const influencerData = getInfluencerData(perfil.influencerId);
        let defaultPrice = 100; // 🔥 CORREÇÃO: Valor padrão mínimo para evitar amount = 0
        
        if (influencerData?.dadosFinanceiros) {
          const serviceMapping: { [key: string]: string } = {
            'instagram_story': 'instagramStory',
            'instagram_reel': 'instagramReel', 
            'youtube_video': 'youtubeDedicated',
            'youtube_shorts': 'youtubeShorts',
            'tiktok_video': 'tiktokVideo',
            'youtube_insertion': 'youtubeInsertion'
          };
          
          const serviceKey = serviceMapping[service] || service;
          const foundPrice = (influencerData.dadosFinanceiros.precos as any)?.[serviceKey]?.price || 
                           (influencerData.dadosFinanceiros.prices as any)?.[serviceKey]?.price || 0;
          
          // 🔥 CORREÇÃO: Garantir que o preço seja maior que 0
          if (foundPrice > 0) {
            defaultPrice = foundPrice;
          }
        }
        
        // 🔥 CORREÇÃO: Garantir que amount seja sempre maior que 0
        if (defaultPrice <= 0) {
          defaultPrice = 100; // Valor padrão mínimo
        }
        
        console.log('📊 Dados para criação do orçamento:', {
          influencerId: perfil.influencerId,
          userId: currentUser.id,
          brandId: proposta.brandId,
          amount: defaultPrice,
          serviceType: service
        });
        
        // Criar orçamento via API
        const createBudgetResponse = await fetch('/api/budgets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            influencerId: perfil.influencerId,
            influencerName: perfil.nomeInfluencer || 'Influenciador',
            userId: currentUser.id,
            brandId: proposta.brandId,
            proposalId: proposta.id,
            amount: defaultPrice,
            currency: 'BRL',
            description: `Orçamento para ${service}`,
            serviceType: service
          }),
        });

        if (!createBudgetResponse.ok) {
          const errorData = await createBudgetResponse.json();
          console.error('❌ Erro na criação do orçamento:', {
            status: createBudgetResponse.status,
            error: errorData.error,
            sentData: {
              influencerId: perfil.influencerId,
              userId: currentUser.id,
              brandId: proposta.brandId,
              amount: defaultPrice
            }
          });
          throw new Error(`Erro ao criar orçamento: ${errorData.error || 'Erro desconhecido'}`);
        }

        const createResult = await createBudgetResponse.json();
        existingBudget = createResult.budget;
        
        console.log('✅ Orçamento criado automaticamente:', existingBudget);
      }

      // 🔥 CORREÇÃO: Usar valor unitário do modal se fornecido, senão valor atual do orçamento
      const currentBudgetValue = unitValue !== undefined ? unitValue : (profileBudgets[profileId]?.[service] || existingBudget.amount || 0);
      
              console.log('💰 Valores para contraproposta:', {
          originalBudgetAmount: existingBudget.amount || 0, // Valor atual do orçamento da proposta
          unitValueFromModal: unitValue, // Valor unitário digitado no modal (se fornecido)
          currentBudgetValue, // Valor final usado (unitValue ou valor atual)
          influencerOriginalPrice: existingBudget.originalPrice || 0, // Apenas para referência
          quantity,
          totalCalculated: currentBudgetValue * quantity
        });

      // Agora que temos um orçamento (existente ou criado), adicionar a quantidade via contraproposta
      const response = await fetch('/api/budgets/counter-proposal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          budgetId: existingBudget.id,
          influencerId: perfil.influencerId,
          userId: currentUser.id,
          originalAmount: existingBudget.amount || 0, // 🔥 CORREÇÃO: Valor atual do orçamento da proposta
          counterAmount: currentBudgetValue, // 🔥 Valor da contraproposta
          note: `Quantidade atualizada para ${quantity}`,
          serviceType: service,
          proposalId: proposta.id,
          type: 'quantity_update',
          quantity: quantity
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Quantidade do serviço salva via counterProposal:', data);

      // Atualizar estado local
      setServiceQuantities(prev => ({
        ...prev,
        [profileId]: {
          ...prev[profileId],
          [service]: quantity
        }
      }));

      toast({
        title: "Quantidade salva",
        description: `Quantidade do serviço ${service} atualizada para ${quantity}`,
      });

    } catch (error) {
      console.error('❌ Erro ao salvar quantidade do serviço:', error);
      toast({
        title: "Erro ao salvar quantidade",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive",
      });
    } finally {
      hideLoader();
    }
  };

  // Função para salvar o orçamento do dialog
  const handleSaveBudget = async () => {
    // 🔒 PROTEÇÃO: Prevenir múltiplos submits
    if (isSavingBudget) {
      console.log('⚠️ Salvamento já em andamento, ignorando...');
      return;
    }

    const numericValue = parseFloat(budgetInputValue.replace(/[^\d,.-]/g, '').replace(',', '.'));
    const quantityValue = parseInt(quantityInputValue) || 1;

    if (isNaN(numericValue) || numericValue < 0) {
      toast({
        title: "Valor inválido",
        description: "Por favor, insira um valor numérico válido.",
        variant: "destructive",
      });
      return;
    }

    if (quantityValue < 1) {
      toast({
        title: "Quantidade inválida",
        description: "A quantidade deve ser pelo menos 1.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSavingBudget(true);
      // Atualizar estados locais primeiro para feedback imediato
      setProfileBudgets(prev => ({
        ...prev,
        [budgetEditData.profileId]: {
          ...prev[budgetEditData.profileId],
          [budgetEditData.service]: numericValue
        }
      }));

      setServiceQuantities(prev => ({
        ...prev,
        [budgetEditData.profileId]: {
          ...prev[budgetEditData.profileId],
          [budgetEditData.service]: quantityValue
        }
      }));
      
      // Salvar no Firebase (orçamento e quantidade)
      await Promise.all([
        saveBudgetToFirebase(budgetEditData.profileId, budgetEditData.service, numericValue),
        saveServiceQuantity(budgetEditData.profileId, budgetEditData.service, quantityValue, numericValue)
      ]);

      toast({
        title: "Orçamento salvo",
        description: `${budgetEditData.serviceName} atualizado: ${formatCurrency(numericValue * quantityValue)} (${quantityValue} serviços)`,
      });

      // Fechar dialog
      setShowBudgetDialog(false);
      setBudgetInputValue('');
      setQuantityInputValue('1');

    } catch (error) {
      console.error('Erro ao salvar orçamento:', error);
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar o orçamento. Tente novamente.",
        variant: "destructive",
      });

      // Reverter estados locais em caso de erro
      setProfileBudgets(prev => ({
        ...prev,
        [budgetEditData.profileId]: {
          ...prev[budgetEditData.profileId],
          [budgetEditData.service]: budgetEditData.currentValue
        }
      }));

      setServiceQuantities(prev => ({
        ...prev,
        [budgetEditData.profileId]: {
          ...prev[budgetEditData.profileId],
          [budgetEditData.service]: budgetEditData.currentQuantity
        }
      }));
    } finally {
      setIsSavingBudget(false);
    }
  };

  // 🔑 FUNÇÃO: Handler para Enter no campo de orçamento
  const handleBudgetInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isSavingBudget && budgetInputValue.trim()) {
      e.preventDefault();
      handleSaveBudget();
    }
  };

  // 🎯 FUNÇÕES PARA PRESETS PERSONALIZADOS
  const loadColumnPresets = async () => {
    try {
      if (!currentUser?.id) return;

      const response = await fetch(`/api/proposals/column-presets?userId=${currentUser.id}`);
      const data = await response.json();

      if (data.success) {
        setColumnPresets(data.presets || []);
        console.log('✅ [PRESETS] Presets carregados:', data.presets?.length || 0);
      }
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao carregar presets:', error);
    }
  };

  const saveColumnPreset = async () => {
    if (!presetName.trim() || !currentUser?.id) return;

    try {
      setIsSavingPreset(true);

      const response = await fetch('/api/proposals/column-presets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUser.id,
          presetName: presetName.trim(),
          visibleColumns,
          columnOrder
        })
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Preset salvo",
          description: `"${presetName}" foi salvo com sucesso!`,
        });

        // ✅ ATUALIZAR IMEDIATAMENTE O DROPDOWN
        setColumnPresets(prev => [data.preset, ...prev]);
        setPresetName('');
        setShowCreatePresetModal(false);
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao salvar preset:', error);
      toast({
        title: "Erro ao salvar preset",
        description: "Não foi possível salvar o preset. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSavingPreset(false);
    }
  };

  const applyColumnPreset = (preset: any) => {
    setVisibleColumns(preset.visibleColumns);
    if (preset.columnOrder?.length > 0) {
      const finalOrder = ['select', ...preset.columnOrder.filter((col: string) => col !== 'select')];
      setColumnOrder(finalOrder);
    }

    // 🎯 DEFINIR PRESET COMO ATIVO
    setActivePreset(preset.id);

    // Salvar como configuração atual
    saveColumnSettingsToDb(preset.visibleColumns, preset.columnOrder || []);

    toast({
      title: "Preset aplicado",
      description: `Configuração "${preset.name}" foi aplicada!`,
    });
  };

  const deleteColumnPreset = async (presetId: string) => {
    try {
      const response = await fetch(`/api/proposals/column-presets?userId=${currentUser?.id}&presetId=${presetId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        setColumnPresets(prev => prev.filter(p => p.id !== presetId));

        // 🎯 LIMPAR PRESET ATIVO SE ELE FOI DELETADO
        if (activePreset === presetId) {
          setActivePreset(null);
        }

        toast({
          title: "Preset removido",
          description: "Preset foi removido com sucesso!",
        });
      }
    } catch (error) {
      console.error('❌ [PRESETS] Erro ao deletar preset:', error);
      toast({
        title: "Erro ao remover preset",
        description: "Não foi possível remover o preset.",
        variant: "destructive",
      });
    }
  };

  const handleCommentChange = async (perfilId: string, comment: string) => {
    if (!proposta || !currentUser) return;
    
    try {
      showLoader("Salvando comentário...");
      
      // Encontrar o perfil para obter o influencerId
      const perfil = proposta.perfis.find(p => p.id === perfilId);
      if (!perfil) return;
      
      // Atualizar comentários na subcoleção do Firebase
      await ProposalService.updateInfluencerComments(
        proposta.id,
        perfil.influencerId,
        comment,
        currentUser.id
      );
      
      // Recarregar dados da proposta
      await loadProposta();
      
      // Fechar modo de edição
      setEditingComment(null);
      setCommentValue('');
      
      toast({
        title: "Comentário salvo",
        description: "O comentário foi salvo na proposta.",
      });
      
    } catch (error) {
      console.error('Erro ao salvar comentário:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o comentário.",
        variant: "destructive",
      });
    } finally {
      hideLoader();
    }
  };

  // 🔧 ETAPA 1: Abrir configurações de compartilhamento (não cria snapshot ainda)
  const handleShareProposal = async () => {
    if (!proposta || !currentUser) {
      toast({
        title: "Erro",
        description: "É necessário estar logado para compartilhar a proposta.",
        variant: "destructive",
      });
      return;
    }

    // ✅ APENAS ABRIR O DIALOG DE CONFIGURAÇÃO
    setShowShareConfigDialog(true);
  };

  // 🔧 ETAPA 2: Criar snapshot com as configurações escolhidas
  const handleCreateSnapshot = async () => {
    if (!proposta || !currentUser) {
      toast({
        title: "Erro",
        description: "É necessário estar logado para criar o snapshot.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSharingLoading(true);

      console.log('🔧 [SNAPSHOT] Criando snapshot personalizado:', {
        proposalId: proposta.id,
        shareSettings
      });

      // Criar compartilhamento com configurações customizadas
      const response = await fetch(`/api/proposals/${proposta.id}/sharing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Configurações básicas
          isPublic: shareSettings.isPublic,
          requiresPassword: shareSettings.requiresPassword,
          password: shareSettings.password,
          expiresIn: shareSettings.expiresIn,
          allowDownload: shareSettings.allowDownload,
          // 🆕 CONFIGURAÇÕES AVANÇADAS DO SNAPSHOT
          snapshotConfig: {
            includeFields: shareSettings.includeFields,
            branding: shareSettings.branding
          }
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar link de compartilhamento');
      }

      // Fechar dialog de configuração e abrir dialog com link
      setShowShareConfigDialog(false);
      setShareUrl(data.data.shareUrl);
      setShowShareDialog(true);

      toast({
        title: "Snapshot criado com sucesso!",
        description: "O link de compartilhamento personalizado foi gerado.",
      });

    } catch (error) {
      console.error('❌ [SNAPSHOT] Erro ao criar snapshot:', error);
      toast({
        title: "Erro ao criar snapshot",
        description: error instanceof Error ? error.message : "Não foi possível criar o link de compartilhamento.",
        variant: "destructive",
      });
    } finally {
      setSharingLoading(false);
    }
  };

  const handleGoBack = () => {
    const currentParams = new URLSearchParams();
    if (brandId) currentParams.set('brandId', brandId);
    
    const backUrl = '/propostas' + (currentParams.toString() ? '?' + currentParams.toString() : '');
    router.push(backUrl);
  };

  // 🔥 FUNÇÃO ULTRA-OTIMIZADA: Adição instantânea com dados completos garantidos
  const handleAddInfluencers = async () => {
    if (!proposta || selectedInfluencers.length === 0) return;

    try {
      showLoader("Adicionando influenciadores...");
      
      console.log('🚀 [ULTRA-SYNC] Iniciando adição de influenciadores:', {
        proposalId: proposta.id,
        selectedInfluencers: selectedInfluencers.length,
        selectedIds: selectedInfluencers
      });

      // 🎯 ESTRATÉGIA 1: Carregar dados COMPLETOS dos influenciadores ANTES de qualquer operação
      console.log('📊 [ULTRA-SYNC] Pré-carregando dados completos dos influenciadores...');
      
      // Limpar cache para garantir dados frescos
      selectedInfluencers.forEach(id => {
        delete influencerCache.current[id];
      });
      
      const newInfluencersData = await loadInfluencersViaGraphQL(selectedInfluencers);
      console.log('✅ [ULTRA-SYNC] Dados pré-carregados:', {
        total: Object.keys(newInfluencersData).length,
        influencers: Object.keys(newInfluencersData).map(id => ({
          id,
          nome: newInfluencersData[id]?.nome,
          temDados: !!newInfluencersData[id]?.nome
        }))
      });

      // ⚠️ VALIDAÇÃO CRÍTICA: Garantir que TODOS os influenciadores têm dados completos
      const influenciadoreSemDados = selectedInfluencers.filter(id => !newInfluencersData[id]?.nome);
      if (influenciadoreSemDados.length > 0) {
        console.warn('⚠️ [ULTRA-SYNC] Encontrados influenciadores sem dados. Tentando recarregar...', influenciadoreSemDados);
        
        // Tentar carregar novamente os problemáticos
        const retryData = await loadInfluencersViaGraphQL(influenciadoreSemDados);
        Object.assign(newInfluencersData, retryData);
        
        // Se ainda há problemas, abortar
        const aindaSemDados = influenciadoreSemDados.filter(id => !newInfluencersData[id]?.nome);
        if (aindaSemDados.length > 0) {
          throw new Error(`Não foi possível carregar dados para ${aindaSemDados.length} influenciador(es). Tente novamente.`);
        }
      }

      // 🎯 ESTRATÉGIA 2: Salvar no Firebase com retry automático
      console.log('💾 [ULTRA-SYNC] Salvando no Firebase...');
      let saveSuccess = false;
      let saveAttempts = 0;
      const maxSaveAttempts = 3;
      
      while (!saveSuccess && saveAttempts < maxSaveAttempts) {
        try {
          await ProposalService.addInfluencersToProposal(
            proposta.id,
            selectedInfluencers,
            currentUser?.id || ''
          );
          saveSuccess = true;
          console.log('✅ [ULTRA-SYNC] Dados salvos no Firebase com sucesso');
        } catch (saveError) {
          saveAttempts++;
          console.warn(`⚠️ [ULTRA-SYNC] Tentativa ${saveAttempts} de salvamento falhou:`, saveError);
          if (saveAttempts < maxSaveAttempts) {
            await new Promise(resolve => setTimeout(resolve, 1000 * saveAttempts));
          } else {
            throw saveError;
          }
        }
      }

      // 🎯 ESTRATÉGIA 3: Criar perfis com dados ultra-completos
      console.log('🔄 [ULTRA-SYNC] Criando perfis com dados completos...');
      
      const novosPerfisUltraCompletos: PropostaPerfil[] = selectedInfluencers.map(influencerId => {
        const influencerData = newInfluencersData[influencerId];
        
        if (!influencerData) {
          console.error(`❌ [ULTRA-SYNC] Dados não encontrados para ${influencerId}`);
        }
        
        // Buscar dados mais completos possíveis
        const nomeInfluencer = influencerData?.nome || 
                              (influencerData as any)?.name || 
                              'Nome não disponível';
        
        const username = influencerData?.redesSociais?.instagram?.username || 
                        influencerData?.redesSociais?.youtube?.username || 
                        influencerData?.redesSociais?.tiktok?.username ||
                        (influencerData as any)?.instagramUsername ||
                        (influencerData as any)?.youtubeUsername ||
                        (influencerData as any)?.tiktokUsername || 'N/A';
        
        const plataforma = influencerData?.mainNetwork || 
                          (influencerData?.redesSociais?.instagram ? 'instagram' : 
                           influencerData?.redesSociais?.youtube ? 'youtube' : 
                           influencerData?.redesSociais?.tiktok ? 'tiktok' : 'instagram');
        
        const seguidores = Math.max(
          influencerData?.redesSociais?.instagram?.seguidores || 0,
          influencerData?.redesSociais?.youtube?.seguidores || 0,
          influencerData?.redesSociais?.tiktok?.seguidores || 0,
          (influencerData as any)?.instagramFollowers || 0,
          (influencerData as any)?.youtubeFollowers || 0,
          (influencerData as any)?.tiktokFollowers || 0
        );
        
        console.log(`✅ [ULTRA-SYNC] Perfil criado: ${nomeInfluencer} (${seguidores} seguidores)`);
        
        return {
          id: `${proposta.id}_${influencerId}`,
          influencerId: influencerId,
          nomeInfluencer,
          username,
          plataforma,
          seguidores,
          status: 'pendente' as const,
          comentarios: '',
          valor: 0,
          dataResposta: new Date()
        };
      });

      // 🎯 ESTRATÉGIA 4: Atualização IMEDIATA e SINCRONA do estado
      console.log('⚡ [ULTRA-SYNC] Atualizando estado de forma IMEDIATA...');
      
      // 🔥 CORREÇÃO PRINCIPAL: Atualizar AMBOS os estados simultaneamente
      const estadoAtualizado = {
        proposta: {
          ...proposta,
          perfis: [...proposta.perfis, ...novosPerfisUltraCompletos],
          influencers: [
            ...proposta.influencers,
            ...selectedInfluencers.map(id => ({ 
              id, 
              name: newInfluencersData[id]?.nome || 'N/A' 
            }))
          ]
        },
        proposalInfluencersData: {
          ...proposalInfluencersData,
          ...newInfluencersData
        }
      };
      
      // 🔥 APLICAR ATUALIZAÇÕES EM ORDEM E DE FORMA IMEDIATA
      // 1. Primeiro atualizar os dados dos influencers
      setProposalInfluencersData(estadoAtualizado.proposalInfluencersData);
      
      // 2. Depois atualizar a proposta (forçar re-render)
      setProposta(estadoAtualizado.proposta);
      
      // 🔥 FORÇA RE-RENDER IMEDIATO das colunas da tabela
      // Atualizar o cache também para garantir acesso imediato
      Object.entries(newInfluencersData).forEach(([id, data]) => {
        if (data?.nome) {
          influencerCache.current[id] = { data, timestamp: Date.now() };
        }
      });

      // 🎯 ESTRATÉGIA 5: Validação pós-adição IMEDIATA
      console.log('🔍 [ULTRA-SYNC] Validando dados adicionados IMEDIATAMENTE...');
      
      // Verificar se todos os perfis foram adicionados corretamente
      const perfisAdicionados = estadoAtualizado.proposta.perfis.filter(perfil => 
        selectedInfluencers.includes(perfil.influencerId)
      );
      
      if (perfisAdicionados.length !== selectedInfluencers.length) {
        console.warn('⚠️ [ULTRA-SYNC] Nem todos os perfis foram adicionados corretamente');
      }
      
      // 🔥 GARANTIR que getInfluencerData encontre os dados imediatamente
      console.log('🔍 [ULTRA-SYNC] Testando acesso aos dados via getInfluencerData...');
      selectedInfluencers.forEach(id => {
        const testData = getInfluencerData(id);
        console.log(`🧪 [TEST] Dados para ${id}:`, {
          hasData: !!testData,
          nome: testData?.nome,
          source: testData ? 'found' : 'not_found'
        });
      });
      
      // 🎯 ESTRATÉGIA 6: Cleanup e feedback ao usuário
      setSelectedInfluencers([]);
      setShowAddInfluencersDialog(false);
      clearModalStates();

      console.log('🎉 [ULTRA-SYNC] Processo completo! Influenciadores adicionados com dados completos IMEDIATAMENTE.');

      toast({
        title: "Influenciadores adicionados!",
        description: `${selectedInfluencers.length} influenciador(es) foram adicionados à proposta com dados completos.`,
      });

      // 🔥 SYNC EVENTUAL REMOVIDA - não é mais necessária pois os dados já estão completos

    } catch (error) {
      console.error('❌ [ULTRA-SYNC] Erro crítico na adição de influenciadores:', error);
      
      // Em caso de erro, fazer fallback para reload completo
      console.log('🔄 [ULTRA-SYNC] Fazendo fallback para reload completo...');
      try {
        await loadProposta();
      } catch (fallbackError) {
        console.error('❌ [ULTRA-SYNC] Erro no fallback:', fallbackError);
      }
      
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível adicionar os influenciadores à proposta.",
        variant: "destructive",
      });
    } finally {
      hideLoader();
    }
  };

  // Função placeholder para carregar marcas disponíveis
  const loadAvailableBrands = async () => {
    console.log('🔍 Carregando marcas disponíveis...');
    // TODO: Implementar carregamento de marcas
  };

  // Função placeholder para carregar influenciadores de uma marca
  const loadInfluencersFromBrand = async (brandId: string) => {
    console.log('🔍 Carregando influenciadores da marca:', brandId);
    // TODO: Implementar carregamento de influenciadores por marca
  };

  // Funções auxiliares movidas para antes do useMemo

  const getInfluencerStatus = (id: string) => {
    // Retorna null para este contexto - usado no dashboard
    return null;
  };

  // Função para remover perfil da proposta
  const handleRemoveProfile = async (perfilId: string) => {
    if (!proposta || !currentUser) {
      toast({
        title: "Erro",
        description: "É necessário estar logado para remover influenciadores.",
        variant: "destructive",
      });
      return;
    }

    try {
      showLoader("Removendo influenciador...");
      
      // Encontrar o perfil para confirmação
      const perfil = proposta.perfis.find(p => p.id === perfilId);
      if (!perfil) {
        toast({
          title: "Erro",
          description: "Perfil não encontrado.",
          variant: "destructive",
        });
        return;
      }

      console.log('🗑️ Removendo perfil da proposta:', {
        proposalId: proposta.id,
        profileId: perfilId,
        influencerName: perfil.nomeInfluencer
      });

      // Remover perfil via API
      const token = await getToken();
      
      const response = await fetch(`/api/proposals/${proposta.id}/influencers/${perfil.influencerId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao remover influenciador');
      }

      // Recarregar dados da proposta
      await loadProposta();

      toast({
        title: "Influenciador removido",
        description: `${perfil.nomeInfluencer} foi removido da proposta com sucesso.`,
      });

    } catch (error) {
      console.error('❌ Erro ao remover perfil:', error);
      toast({
        title: "Erro ao remover",
        description: error instanceof Error ? error.message : "Não foi possível remover o influenciador da proposta.",
        variant: "destructive",
      });
    } finally {
      hideLoader();
    }
  };

  const renderStatusBadge = (status: string) => {
    const statusConfig = {
      'aceito': { label: 'Aceito', color: 'bg-green-100 text-green-700' },
      'rejeitado': { label: 'Rejeitado', color: 'bg-red-100 text-red-700' },
      'pendente': { label: 'Pendente', color: 'bg-yellow-100 text-yellow-700' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pendente;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const openProposalSheet = (influencer: any) => {
    // Função para abrir sheet de proposta - implementar se necessário
    console.log('Abrir proposta para:', influencer.nome);
  };

  const toggleRowExpansion = (id: string) => {
    setExpandedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  // Funções de orçamento customizado removidas

  // Cache movido para o início do componente

  // Métricas de performance
  const performanceMetrics = useRef({
    loadTimes: {
      proposta: 0,
      influencers: 0,
      cache: 0
    },
    cacheStats: {
      hits: 0,
      misses: 0,
      hitRate: 0
    },
    apiCalls: {
      total: 0,
      batched: 0,
      saved: 0
    }
  });

  // Função para registrar métricas
  const recordMetric = (type: string, duration: number, extra?: any) => {
    const metrics = performanceMetrics.current;
    
    switch (type) {
      case 'loadProposta':
        metrics.loadTimes.proposta = duration;
        break;
      case 'loadInfluencers':
        metrics.loadTimes.influencers = duration;
        break;
      case 'cacheHit':
        metrics.cacheStats.hits++;
        metrics.cacheStats.hitRate = metrics.cacheStats.hits / (metrics.cacheStats.hits + metrics.cacheStats.misses);
        break;
      case 'cacheMiss':
        metrics.cacheStats.misses++;
        metrics.cacheStats.hitRate = metrics.cacheStats.hits / (metrics.cacheStats.hits + metrics.cacheStats.misses);
        break;
      case 'apiCall':
        metrics.apiCalls.total++;
        if (extra?.batched) metrics.apiCalls.batched++;
        break;
    }

    // Log de performance a cada 10 operações
    if ((metrics.cacheStats.hits + metrics.cacheStats.misses) % 10 === 0) {
      console.log('📊 Métricas de Performance:', {
        loadTimes: metrics.loadTimes,
        cache: `${(metrics.cacheStats.hitRate * 100).toFixed(1)}% hit rate`,
        apiOptimization: `${metrics.apiCalls.batched}/${metrics.apiCalls.total} calls batched`
      });
    }
  };

  // ✅ FUNÇÃO ULTRA-OTIMIZADA: Carregamento inteligente com merge de dados
  const loadProposalInfluencersData = async (influencerIds: string[]) => {
    if (influencerIds.length === 0) return;
    
    try {
      console.log('🔍 [PROPOSAL-DATA] Carregando dados dos influenciadores...', {
        ids: influencerIds,
        totalIds: influencerIds.length,
        timestamp: new Date().toISOString()
      });
      
      const now = Date.now();
      const cachedData: { [key: string]: Influencer } = {};
      const uncachedIds: string[] = [];
      
      // Verificar cache primeiro, mas ser mais criterioso
      influencerIds.forEach(id => {
        const cached = influencerCache.current[id];
        if (cached && (now - cached.timestamp) < CACHE_DURATION && cached.data?.nome) {
          cachedData[id] = cached.data;
          recordMetric('cacheHit', 0);
          console.log(`📦 [PROPOSAL-DATA] Cache válido para ${cached.data.nome} (${id})`);
        } else {
          uncachedIds.push(id);
          recordMetric('cacheMiss', 0);
          console.log(`🌐 [PROPOSAL-DATA] Precisa carregar dados para ${id}`);
        }
      });
      
      // Carregar apenas os não cacheados com retry
      let newData: { [key: string]: Influencer } = {};
      if (uncachedIds.length > 0) {
        console.log(`🚀 [PROPOSAL-DATA] Carregando ${uncachedIds.length} influenciadores via GraphQL...`);
        
        try {
          newData = await loadInfluencersViaGraphQL(uncachedIds);
          
          // Validar dados carregados
          const dadosValidos = Object.entries(newData).filter(([id, data]) => data?.nome);
          const dadosInvalidos = Object.entries(newData).filter(([id, data]) => !data?.nome);
          
          console.log(`✅ [PROPOSAL-DATA] Carregamento concluído:`, {
            dadosValidos: dadosValidos.length,
            dadosInvalidos: dadosInvalidos.length,
            detalhesValidos: dadosValidos.map(([id, data]) => ({ id, nome: data.nome }))
          });
          
          // Se há dados inválidos, tentar recarregar uma vez
          if (dadosInvalidos.length > 0) {
            console.warn(`⚠️ [PROPOSAL-DATA] Encontrados ${dadosInvalidos.length} dados inválidos. Tentando recarregar...`);
            const retryIds = dadosInvalidos.map(([id]) => id);
            const retryData = await loadInfluencersViaGraphQL(retryIds);
            Object.assign(newData, retryData);
          }
          
          // Atualizar cache com dados válidos
          Object.entries(newData).forEach(([id, data]) => {
            if (data?.nome) {
              influencerCache.current[id] = { data, timestamp: now };
              console.log(`💾 [PROPOSAL-DATA] Dados armazenados no cache: ${data.nome}`);
            }
          });
          
        } catch (error) {
          console.error('❌ [PROPOSAL-DATA] Erro ao carregar dados via GraphQL:', error);
          // Em caso de erro, ainda tentar usar dados do cache se disponíveis
        }
      }
      
      // 🎯 MERGE INTELIGENTE: Combinar dados existentes + cache + novos dados
      const dadosExistentes = proposalInfluencersData;
      const allData = { 
        ...dadosExistentes, // Manter dados que já tínhamos
        ...cachedData,      // Adicionar dados do cache
        ...newData          // Adicionar dados novos (sobrescrever se necessário)
      };
      
      console.log(`✅ [PROPOSAL-DATA] Merge completo:`, {
        totalInfluencers: Object.keys(allData).length,
        dadosExistentes: Object.keys(dadosExistentes).length,
        doCache: Object.keys(cachedData).length,
        novos: Object.keys(newData).length,
        detalhes: Object.entries(allData).map(([id, data]) => ({
          id: id.substring(0, 8),
          nome: data?.nome || 'SEM_NOME',
          temDados: !!data?.nome
        }))
      });
      
      // Atualizar estado apenas se houve mudanças significativas
      const houveMudancas = Object.keys(allData).length > Object.keys(dadosExistentes).length ||
                           Object.keys(newData).length > 0;
      
      if (houveMudancas) {
        setProposalInfluencersData(allData);
        console.log('🔄 [PROPOSAL-DATA] Estado atualizado com novos dados');
      } else {
        console.log('ℹ️ [PROPOSAL-DATA] Nenhuma mudança detectada, mantendo estado atual');
      }
      
    } catch (error) {
      console.error('❌ [PROPOSAL-DATA] Erro crítico ao carregar dados dos influenciadores:', error);
    }
  };

  // ✅ EFFECT ULTRA-OTIMIZADO: Sincronização imediata dos dados dos influenciadores
  useEffect(() => {
    if (proposta?.perfis && proposta.perfis.length > 0) {
      const influencerIds = proposta.perfis.map(p => p.influencerId);
      
      console.log('🔄 [SYNC-EFFECT] Sincronizando dados dos influenciadores:', {
        totalPerfis: proposta.perfis.length,
        influencerIds,
        timestamp: new Date().toISOString()
      });
      
      // 🔥 VERIFICAÇÃO OTIMIZADA: Incluir dados do cache no cálculo
      const dadosExistentes = Object.keys(proposalInfluencersData);
      const dadosEmCache = Object.keys(influencerCache.current);
      
      const influenciadoreSemDados = influencerIds.filter(id => {
        // Tem dados na proposta?
        if (proposalInfluencersData[id]?.nome) return false;
        
        // Tem dados válidos no cache?
        const cached = influencerCache.current[id];
        if (cached && cached.data?.nome && (Date.now() - cached.timestamp) < CACHE_DURATION) {
          return false;
        }
        
        return true;
      });
      
      console.log('📊 [SYNC-EFFECT] Análise otimizada dos dados:', {
        dadosExistentes: dadosExistentes.length,
        dadosEmCache: dadosEmCache.length,
        influenciadoreSemDados: influenciadoreSemDados.length,
        listaSemDados: influenciadoreSemDados
      });
      
      // 🔥 CARREGAR DADOS FALTANTES COM PRIORIDADE ALTA
      if (influenciadoreSemDados.length > 0) {
        console.log('🚀 [SYNC-EFFECT] Carregando dados faltantes com PRIORIDADE ALTA:', influenciadoreSemDados);
        
        // Carregar com fallback para garantir dados
        loadProposalInfluencersData(influenciadoreSemDados).then(() => {
          console.log('✅ [SYNC-EFFECT] Dados carregados, forçando re-render...');
          
          // 🔥 FORÇA RE-RENDER das colunas após carregamento
          // Isso garante que mesmo dados carregados assincronamente apareçam
          setProposta(prev => prev ? { ...prev } : prev);
        });
      }
      
      // Carregar orçamentos salvos da subcoleção
      loadBudgetsFromFirebase();
      
      // 🆕 Carregar quantidades dos serviços
      loadServiceQuantities();
    }
  }, [proposta?.perfis?.length]);

  // Processamento de perfis removido - usar dados simples da proposta

  // Componente para criação de orçamento expandido
  // Componente BudgetCreationRow removido - será reimplementado

  // Componente para exibir dados demográficos expandidos
  const ExpandedRowContent = ({ influencer }: { influencer: Influencer }) => {
    const { audienceGender, audienceAgeRanges } = influencer;
    
    if (!audienceGender && !audienceAgeRanges) {
      return (
        <div className="p-6 bg-muted/20 border-t">
          <p className="text-muted-foreground text-center">Dados demográficos não disponíveis</p>
        </div>
      );
    }

    const genderChartConfig = {
      female: {
        label: "Feminino",
        color: "hsl(328, 45%, 65%)",
      },
      male: {
        label: "Masculino",
        color: "hsl(207, 45%, 65%)",
      },
      other: {
        label: "Outros",
        color: "hsl(272, 45%, 65%)",
      },
    };

    const ageChartConfig = {
      "13-17": { label: "13-17", color: "hsl(260, 45%, 70%)" },
      "18-24": { label: "18-24", color: "hsl(187, 45%, 70%)" },
      "25-34": { label: "25-34", color: "hsl(160, 45%, 70%)" },
      "35-44": { label: "35-44", color: "hsl(43, 45%, 70%)" },
      "45-54": { label: "45-54", color: "hsl(0, 45%, 70%)" },
      "55-64": { label: "55-64", color: "hsl(260, 45%, 70%)" },
      "65+": { label: "65+", color: "hsl(220, 20%, 65%)" },
    };

    return (
      <div className="p-4 bg-muted/20 border-t">
        <div className="mb-4">
          <h4 className="text-base font-semibold mb-1">Dados Demográficos da Audiência</h4>
          <p className="text-xs text-muted-foreground">Distribuição de gênero e faixa etária por rede social</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Coluna de Gênero */}
          {audienceGender && (
            <Card className="p-4">
              <h5 className="text-sm font-medium mb-4 text-muted-foreground flex items-center justify-center gap-2">
                <Users className="h-4 w-4" />
                Distribuição por Gênero
              </h5>
              <div className="w-full max-w-3xl mx-auto">
                <ChartContainer config={genderChartConfig} className="h-[300px] w-full">
                  <BarChart
                    data={[
                      {
                        platform: 'Instagram',
                        feminino: audienceGender.instagram?.female || 0,
                        masculino: audienceGender.instagram?.male || 0,
                        outros: audienceGender.instagram?.other || 0,
                      },
                      {
                        platform: 'YouTube',
                        feminino: audienceGender.youtube?.female || 0,
                        masculino: audienceGender.youtube?.male || 0,
                        outros: audienceGender.youtube?.other || 0,
                      },
                      {
                        platform: 'TikTok',
                        feminino: audienceGender.tiktok?.female || 0,
                        masculino: audienceGender.tiktok?.male || 0,
                        outros: audienceGender.tiktok?.other || 0,
                      },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="platform" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <ChartLegend content={<ChartLegendContent />} />
                    <Bar dataKey="feminino" fill="hsl(330 81% 60%)" name="Feminino" />
                    <Bar dataKey="masculino" fill="hsl(221 83% 53%)" name="Masculino" />
                    <Bar dataKey="outros" fill="hsl(262 83% 58%)" name="Outros" />
                  </BarChart>
                </ChartContainer>
              </div>
            </Card>
          )}

          {/* Coluna de Faixa Etária */}
          {audienceAgeRanges && (
            <Card className="p-4">
              <h5 className="text-sm font-medium mb-4 text-muted-foreground flex items-center justify-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Distribuição por Faixa Etária
              </h5>
              <div className="flex justify-center">
                <div className="w-full max-w-3xl">
                  <ChartContainer
                    config={{
                      instagram: { label: "Instagram", color: "hsl(330 81% 60%)" },
                      youtube: { label: "YouTube", color: "hsl(0 72% 51%)" },
                      tiktok: { label: "TikTok", color: "hsl(215 20% 65%)" },
                    }}
                    className="h-[300px] w-full"
                  >
                    <BarChart
                      data={(() => {
                        const allAgeRanges = new Set();
                        const platforms = ['instagram', 'youtube', 'tiktok'] as const;
                        
                        // Coletar todas as faixas etárias
                        platforms.forEach(platform => {
                          const platformData = audienceAgeRanges[platform];
                          if (platformData) {
                            platformData.forEach((item: { ageRange: string; percentage: number }) => {
                              if (item.ageRange && item.percentage > 0) {
                                allAgeRanges.add(item.ageRange);
                              }
                            });
                          }
                        });
                        
                        // Criar dados do gráfico
                        return Array.from(allAgeRanges)
                          .sort((a, b) => {
                            const getMinAge = (range: string) => parseInt(range.split('-')[0]) || 0;
                            return getMinAge(a as string) - getMinAge(b as string);
                          })
                          .map(ageRange => {
                            const item: any = { ageRange };
                            platforms.forEach(platform => {
                              const platformData = audienceAgeRanges[platform];
                              const found = platformData?.find((d: { ageRange: string; percentage: number }) => d.ageRange === ageRange);
                              item[platform] = found?.percentage || 0;
                            });
                            return item;
                          });
                      })()} 
                      margin={{ top: 40, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="ageRange" 
                        className="text-xs fill-muted-foreground"
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        className="text-xs fill-muted-foreground"
                        tick={{ fontSize: 12 }}
                      />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      {audienceAgeRanges.instagram && (
                        <Bar 
                          dataKey="instagram" 
                          fill="hsl(330 81% 60%)" 
                          radius={[2, 2, 0, 0]}
                          name="Instagram"
                        />
                      )}
                      {audienceAgeRanges.youtube && (
                        <Bar 
                          dataKey="youtube" 
                          fill="hsl(0 72% 51%)" 
                          radius={[2, 2, 0, 0]}
                          name="YouTube"
                        />
                      )}
                      {audienceAgeRanges.tiktok && (
                        <Bar 
                          dataKey="tiktok" 
                          fill="hsl(215 20% 65%)" 
                          radius={[2, 2, 0, 0]}
                          name="TikTok"
                        />
                      )}
                    </BarChart>
                  </ChartContainer>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    );
  };

  // ❌ REMOVER LOADER MANUAL - USAR APENAS O CONTROLE GLOBAL

  if (!proposta) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Proposta não encontrada</h2>
          <p className="text-muted-foreground mb-4">A proposta solicitada não existe ou foi removida.</p>
          <Button onClick={handleGoBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar para Propostas
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header com Breadcrumb */}
      <div className="p-4 border-b dark:border-[#1c1627] dark:bg-[#0d0814c2] bg-background overflow-hidden">
        <div className="flex items-center justify-between min-w-0">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>
            
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink onClick={handleGoBack} className="cursor-pointer">
                    Propostas
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{proposta.nome}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          
          <div className="flex justify-end items-center gap-6 min-w-0 flex-1 overflow-hidden">
            {/* Estatísticas dos Perfis */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground overflow-x-auto scrollbar-hide">
              {/* Total de Perfis */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <Users className="h-4 w-4" />
                <span className="font-medium text-foreground">{proposta?.perfis?.length || 0}</span>
                <span>Meus Perfis</span>
              </div>

              {/* Aceitos */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <Check className="h-4 w-4 text-green-600" />
                <span className="font-medium text-foreground">{proposta?.perfis?.filter(p => p.status === 'aceito').length || 0}</span>
                <span>Aceite</span>
              </div>

              {/* Em Reserva */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <Clock className="h-4 w-4" style={{color: '#9810fa'}} />
                <span className="font-medium text-foreground">{proposta?.perfis?.filter(p => p.status === 'pendente').length || 0}</span>
                <span>Em Reserva</span>
              </div>

              {/* Rejeitados */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <X className="h-4 w-4" style={{color: '#ec003f'}} />
                <span className="font-medium text-foreground">{proposta?.perfis?.filter(p => p.status === 'rejeitado').length || 0}</span>
                <span>Rejeitado</span>
              </div>

              {/* Não Definido */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <span className="h-4 w-4 flex items-center justify-center rounded-full border-2 border-gray-400 text-gray-400">
                  <span className="text-xs">?</span>
                </span>
                <span className="font-medium text-foreground">{proposta?.perfis?.filter(p => !p.status).length || 0}</span>
                <span>Pendente</span>
              </div>

              {/* Instagram */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                  <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                </svg>
                <span className="font-medium text-foreground">
                  {proposta?.perfis?.filter(p => {
                    const influencerData = getInfluencerData(p.influencerId);
                    return influencerData?.redesSociais?.instagram;
                  }).length || 0}
                </span>
                <span>Instagram</span>
              </div>

              {/* TikTok */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512" className="text-black dark:text-white">
                  <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                </svg>
                <span className="font-medium text-foreground">
                  {proposta?.perfis?.filter(p => {
                    const influencerData = getInfluencerData(p.influencerId);
                    return influencerData?.redesSociais?.tiktok;
                  }).length || 0}
                </span>
                <span>TikTok</span>
              </div>

              {/* YouTube */}
              <div className="flex items-center gap-1 whitespace-nowrap flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                  <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                </svg>
                <span className="font-medium text-foreground">
                  {proposta?.perfis?.filter(p => {
                    const influencerData = getInfluencerData(p.influencerId);
                    return influencerData?.redesSociais?.youtube;
                  }).length || 0}
                </span>
                <span>YouTube</span>
              </div>
            </div>
            
          
          </div>
        </div>
      </div>

      {/* Layout Principal */}
      <div className="flex-1 p-6 max-w-full overflow-hidden">
        <div className="gap-6 h-full">
          {/* Sidebar com Informações da Proposta - Colapsível */}
          {/*<div className="lg:col-span-1">
            <Collapsible open={infoExpanded} onOpenChange={setInfoExpanded}>
              <Card className="h-fit">
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Informações sobre a Proposta</CardTitle>
                      <ChevronDown 
                        className={`h-4 w-4 transition-transform duration-200 ${
                          infoExpanded ? 'transform rotate-180' : ''
                        }`} 
                      />
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="space-y-4 pt-0">
                    <div>
                      <span className="text-sm text-muted-foreground block mb-1">Nome da proposta</span>
                      <p className="font-medium">{proposta.nome}</p>
                    </div>
                    
                    {proposta.descricao && (
                      <div>
                        <span className="text-sm text-muted-foreground block mb-1">Descrição</span>
                        <p className="text-sm">{proposta.descricao}</p>
                      </div>
                    )}
                    
                    <Separator />
                    
                    <div>
                      <span className="text-sm text-muted-foreground block mb-1">Número de perfis</span>
                      <p className="font-medium text-lg">{proposta.perfis.length}</p>
                    </div>
                    
                    <div>
                      <span className="text-sm text-muted-foreground block mb-1">Data de criação</span>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{proposta.dataCriacao.toLocaleDateString('pt-BR')}</span>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-sm text-muted-foreground block mb-1">Última atualização</span>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{proposta.ultimaAtualizacao.toLocaleDateString('pt-BR')}</span>
                      </div>
                    </div>
                    
                    {proposta.criadoPor && (
                      <div>
                        <span className="text-sm text-muted-foreground block mb-1">Criado por</span>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{proposta.criadoPor}</span>
                        </div>
                      </div>
                    )}
                    
                    {proposta.totalAmount > 0 && (
                      <div>
                        <span className="text-sm text-muted-foreground block mb-1">Valor total</span>
                        <p className="font-medium text-lg text-[#ec003f]">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(proposta.totalAmount)}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>
          </div>*/}



          {/* Área dos Filtros e Conteúdo Principal */}
          <div className="w-full max-w-full h-full flex flex-col gap-6 overflow-hidden">
            

            {/* Conteúdo da Tabela ou Cards */}
            <div className="flex-1 max-w-full dark:bg-[#0d0814c2] dark:border-[#1c1627] rounded-lg overflow-hidden flex flex-col">
              {!proposta || !proposta.perfis || proposta.perfis.length === 0 ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <Users className="h-12 w-12 text-gray-300 mb-4 mx-auto" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Nenhum influenciador adicionado
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-6 max-w-sm text-center">
                      Comece adicionando influenciadores à sua proposta usando os botões acima.
                    </p>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white hover:from-[#ff0074]/90 hover:to-[#9810fa]/90"
                          size="sm"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar primeiro influenciador
                          <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="center">
                        <DropdownMenuItem onClick={() => {
                          console.log('🚀 Clicando em "De influencers"');
                          loadAllInfluencers(); // 🔥 AGORA IMPLEMENTADA
                          setShowAddInfluencersDialog(true);
                        }}>
                          <Users className="h-4 w-4 mr-2" />
                          De influencers
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          loadAvailableBrands();
                          setShowBrandSelectionDialog(true);
                        }}>
                          <Users className="h-4 w-4 mr-2" />
                          De uma marca
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ) : (
                <div className="flex-1 overflow-hidden">
                  {viewMode === 'table' ? (
                    // View de Tabela separando Toolbar da Tabela
                    <div className="h-full flex flex-col">
                      {/* Toolbar fixo (sem overflow) */}
                      <div className="flex items-center justify-between p-4  shrink-0">
                        <div className="flex items-center gap-2">
                          <Input
                            placeholder="Buscar influenciadores..."
                            className="w-80"
                          />
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={forceReloadProposal}
                            disabled={isLoading}
                            className="border dark:border-[#1c1627] hover:bg-gray-50"
                          >
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
                            Atualizar
                          </Button>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white hover:bg-[#ec003f]/90"
                                size="sm"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Adicionar perfis
                                <ChevronDown className="h-4 w-4 ml-2" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                console.log('🚀 Clicando em "De influencers"');
                                loadAllInfluencers(); // 🔥 BUSCAR INFLUENCIADORES VIA GRAPHQL
                                setShowAddInfluencersDialog(true);
                              }}>
                                <Users className="h-4 w-4 mr-2" />
                                De influencers
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                loadAvailableBrands();
                                setShowBrandSelectionDialog(true);
                              }}>
                                <Users className="h-4 w-4 mr-2" />
                                De uma marca
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                size="sm" 
                                className="bg-[#9810fa] text-white hover:bg-[#9810fa]/90"
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                Ações {selectedProposalInfluencers.length > 0 && `(${selectedProposalInfluencers.length})`}
                            <ChevronDown className="h-4 w-4 ml-2" />
                          </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Aceitar apenas os perfis selecionados
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'aceito');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <Check className="h-4 w-4 mr-2 text-green-600" />
                                Aceitar Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Rejeitar apenas os perfis selecionados
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'rejeitado');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <X className="h-4 w-4 mr-2 text-red-600" />
                                Rejeitar Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Marcar apenas os selecionados como pendente
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'pendente');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <Clock className="h-4 w-4 mr-2 text-yellow-600" />
                                Marcar Selecionados como Pendente ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Ação para remover apenas os selecionados (com confirmação)
                                  if (confirm(`Tem certeza que deseja remover ${selectedProposalInfluencers.length} influenciador(es) selecionado(s) desta proposta?`)) {
                                    selectedProposalInfluencers.forEach(profileId => {
                                      handleRemoveProfile(profileId);
                                    });
                                    setSelectedProposalInfluencers([]);
                                  }
                                }}
                                className="text-red-600"
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <X className="h-4 w-4 mr-2" />
                                Remover Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setShowColumnDialog(true)}
                            className="border dark:border-[#1c1627] hover:bg-gray-50"
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            Colunas
                          </Button>
                          
                          {/* Toggle de View Mode */}
                          <div className="flex items-center gap-1 p-1 h-9 bg-muted rounded-lg border">
                            <Button
                              variant={viewMode === 'table' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setViewMode('table')}
                              className={`gap-2 h-8 px-3 ${viewMode === 'table' ? 'bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <TableIcon className="h-4 w-4" />
                              Tabela
                            </Button>
                            <Button
                              variant={viewMode === 'card' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setViewMode('card')}
                              className={`gap-2 h-8 px-3 ${viewMode === 'card' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <Grid3X3 className="h-4 w-4" />
                              Cards
                            </Button>
                          </div>

                          {/* 🆕 TOGGLE PARA PACOTES DE SERVIÇOS */}
                          <div className="flex items-center gap-1 p-1 h-9 bg-muted rounded-lg border">
                            <Button
                              variant={packageMode === 'individual' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setPackageMode('individual')}
                              className={`gap-2 h-8 px-3 ${packageMode === 'individual' ? 'bg-gradient-to-r from-[#5600ce] to-[#ff0074] text-white shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <Briefcase className="h-4 w-4" />
                              Serviços Individuais
                            </Button>
                            <Button
                              variant={packageMode === 'packages' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setPackageMode('packages')}
                              className={`gap-2 h-8 px-3 ${packageMode === 'packages' ? 'bg-gradient-to-r from-[#5600ce] to-[#ff0074] text-white shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <Package className="h-4 w-4" />
                              Pacotes
                            </Button>
                          </div>
                        </div>
                        
                       
                      </div>

                      {/* Área da tabela com overflow horizontal */}
                      <div className="flex-1 overflow-x-auto">
                        
                          <DataTable
                            columns={influencerColumns}
                            data={adaptPropostaPerfisForTable}
                            searchKey="nomeInfluencer"
                            searchPlaceholder="Buscar influenciadores..."
                            className="w-full h-full"
                            enableRowSelection={true}
                            enableColumnOrdering={true}
                            pageSize={20}
                            columnOrder={columnOrder}
                            onColumnOrderChange={handleColumnOrderChange}
                            expandedRows={expandedRows}
                            rowSelection={rowSelection}
                            onRowSelectionChange={handleRowSelectionChange}
                            renderExpandButton={(row) => {
                              return null; // Funcionalidade removida
                            }}
                            renderExpandedRow={(influencer) => {
                              // Expansão de linha removida - será reimplementada
                              return null;
                            }}
                            toolbarActions={<></>} // Toolbar vazio pois está separado acima
                          />
                        </div>
                      </div>
                    
                  ) : (
                    // View de Cards
                    <div className="h-full flex flex-col">
                      {/* Toolbar para Cards */}
                      <div className="flex items-center justify-between p-4 border-b">
                        <div className="flex items-center gap-2 h-9">
                          <Input
                            placeholder="Buscar influenciadores..."
                            className="w-80"
                          />
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                className="bg-[#ec003f] text-white hover:bg-[#ec003f]/90"
                                size="sm"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Adicionar perfis
                                <ChevronDown className="h-4 w-4 ml-2" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                console.log('🚀 Clicando em "De influencers"');
                                loadAllInfluencers(); // 🔥 BUSCAR INFLUENCIADORES VIA GRAPHQL
                                setShowAddInfluencersDialog(true);
                              }}>
                                <Users className="h-4 w-4 mr-2" />
                                De influencers
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                loadAvailableBrands();
                                setShowBrandSelectionDialog(true);
                              }}>
                                <Users className="h-4 w-4 mr-2" />
                                De uma marca
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                size="sm" 
                                className="bg-[#9810fa] text-white hover:bg-[#9810fa]/90"
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                Ações {selectedProposalInfluencers.length > 0 && `(${selectedProposalInfluencers.length})`}
                            <ChevronDown className="h-4 w-4 ml-2" />
                          </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Aceitar apenas os perfis selecionados
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'aceito');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <Check className="h-4 w-4 mr-2 text-green-600" />
                                Aceitar Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Rejeitar apenas os perfis selecionados
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'rejeitado');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <X className="h-4 w-4 mr-2 text-red-600" />
                                Rejeitar Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Marcar apenas os selecionados como pendente
                                  selectedProposalInfluencers.forEach(profileId => {
                                    handleStatusChange(profileId, 'pendente');
                                  });
                                  setSelectedProposalInfluencers([]);
                                }}
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <Clock className="h-4 w-4 mr-2 text-yellow-600" />
                                Marcar Selecionados como Pendente ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => {
                                  // Ação para remover apenas os selecionados (com confirmação)
                                  if (confirm(`Tem certeza que deseja remover ${selectedProposalInfluencers.length} influenciador(es) selecionado(s) desta proposta?`)) {
                                    selectedProposalInfluencers.forEach(profileId => {
                                      handleRemoveProfile(profileId);
                                    });
                                    setSelectedProposalInfluencers([]);
                                  }
                                }}
                                className="text-red-600"
                                disabled={selectedProposalInfluencers.length === 0}
                              >
                                <X className="h-4 w-4 mr-2" />
                                Remover Selecionados ({selectedProposalInfluencers.length})
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          {/* Toggle de View Mode para Cards */}
                          <div className="flex items-center gap-1 p-1 bg-muted rounded-lg border">
                            <Button
                              variant={viewMode === 'table' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setViewMode('table')}
                              className={`gap-2 h-8 px-3 ${viewMode === 'table' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <TableIcon className="h-4 w-4" />
                              Tabela
                            </Button>
                            <Button
                              variant={viewMode === 'card' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setViewMode('card')}
                              className={`gap-2 h-8 px-3 ${viewMode === 'card' ? 'bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white shadow-sm' : 'text-muted-foreground hover:text-foreground'}`}
                            >
                              <Grid3X3 className="h-4 w-4" />
                              Cards
                            </Button>
                          </div>
                        </div>
                      </div>
                      
                      {/* Grid de Cards */}
                      <div className="flex-1 p-4 overflow-y-auto">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                          {proposta.perfis.map((perfil) => {
                            const influencerData = getInfluencerData(perfil.influencerId);
                            
                            // Adaptar dados para o InfluencerCard
                            const adaptedInfluencer = {
                              ...influencerData,
                              id: perfil.influencerId,
                              name: perfil.nomeInfluencer || influencerData?.nome || 'Nome não disponível',
                              location: influencerData?.location || `${influencerData?.cidade}, ${influencerData?.estado}` || 'Local não informado',
                              mainCategories: (influencerData as any)?.mainCategories || [influencerData?.categoria] || [],
                              mainCategoriesData: (influencerData as any)?.mainCategoriesData || [],
                              instagramFollowers: (influencerData as any)?.instagramFollowers || influencerData?.redesSociais?.instagram?.seguidores || 0,
                              instagramUsername: (influencerData as any)?.instagramUsername || influencerData?.redesSociais?.instagram?.username || '',
                              tiktokFollowers: (influencerData as any)?.tiktokFollowers || influencerData?.redesSociais?.tiktok?.seguidores || 0,
                              youtubeFollowers: (influencerData as any)?.youtubeFollowers || influencerData?.redesSociais?.youtube?.seguidores || 0,
                              whatsapp: influencerData?.whatsapp || '',
                              financialData: {
                                email: influencerData?.email || influencerData?.dadosFinanceiros?.emailFinanceiro || ''
                              }
                            };
                            
                            return (
                              <InfluencerCard
                                key={perfil.id}
                                influencer={adaptedInfluencer as any}
                                onClick={(influencer) => {
                                  console.log('Card clicado:', influencer);
                                  // Aqui você pode implementar a ação desejada
                                }}
                                isSelected={false}
                                selectionMode={false}
                                onToggleSelection={() => {}}
                                isInProposal={true}
                                proposalId={propostaId}
                              />
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

                    </div>
        </div>
      </div>

      {/* Dialog: Selecionar Marca */}
      <Dialog 
        open={showBrandSelectionDialog}
        modal={true}
        onOpenChange={(open) => {
          setShowBrandSelectionDialog(open);
          if (!open) {
            clearModalStates();
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Selecionar marca</DialogTitle>
          </DialogHeader>
          
          <div className="bg-[#9810fa] text-white p-6 rounded-t-lg">
            <h2 className="text-xl font-semibold">Selecionar marca</h2>
          </div>

          <div className="p-6 space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Pesquisar marcas..."
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
              {brandInfluencersLoading ? (
                <div className="col-span-full flex items-center justify-center py-8">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#9810fa]"></div>
                    
                  </div>
                </div>
              ) : (
                availableBrands.map((brand) => (
                  <div
                    key={brand.id}
                    className="border rounded-lg p-4 cursor-pointer hover:bg-muted/30 hover:border-[#9810fa] transition-colors"
                    onClick={() => {
                      setSelectedBrandForInfluencers(brand.id);
                      loadInfluencersFromBrand(brand.id);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                        {brand.logo ? (
                          <img 
                            src={brand.logo} 
                            alt={brand.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-[#9810fa] rounded flex items-center justify-center text-white font-bold">
                            {brand.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium truncate">{brand.name}</h3>
                        {brand.industry && (
                          <p className="text-sm text-muted-foreground truncate">{brand.industry}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
              
              {availableBrands.length === 0 && (
                <div className="col-span-full text-center py-8 text-muted-foreground">
                  Nenhuma marca encontrada
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 p-6 border-t bg-muted/30">
            <Button 
              variant="outline" 
              onClick={() => {
                setShowBrandSelectionDialog(false);
                clearModalStates();
              }}
            >
              Cancelar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog: Selecionar Influencers */}
      <Dialog 
        open={showAddInfluencersDialog}
        modal={true}
        onOpenChange={(open) => {
          setShowAddInfluencersDialog(open);
          if (!open) {
            // Garantir que o modal está completamente fechado
            clearModalStates();
          }
        }}
      >
        <DialogContent className="max-w-5xl max-h-[90vh] p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Selecionar influencers</DialogTitle>
          </DialogHeader>
          
          <div className="bg-[#9810fa] text-white p-6 rounded-t-lg">
            <h2 className="text-xl font-semibold">
              {selectedBrandForInfluencers ? 
                `Influencers da marca ${availableBrands.find(b => b.id === selectedBrandForInfluencers)?.name || 'selecionada'}` :
                'Selecionar influencers'
              }
              {influencers.length > 0 && !selectedBrandForInfluencers && (
                <span className="text-sm font-normal ml-2 opacity-80">
                  ({influencers.length} disponíveis)
                </span>
              )}
            </h2>
          </div>

          <div className="p-6 space-y-4">
            {/* Header de Filtros */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Pesquisar"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[100px]">
                  <option>Perfis</option>
                  <option>Instagram</option>
                  <option>TikTok</option>
                  <option>YouTube</option>
                  <option>Todos</option>
                </select>

                <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[100px]">
                  <option>E-mail</option>
                  <option>Com e-mail</option>
                  <option>Sem e-mail</option>
                </select>

                <select className="h-10 px-3 py-2 border rounded-md text-sm bg-background min-w-[140px]">
                  <option>Data adicionada</option>
                  <option>Mais recente</option>
                  <option>Mais antigo</option>
                  <option>Esta semana</option>
                  <option>Este mês</option>
                </select>
              </div>
            </div>

            {/* Tabela de Influenciadores */}
            <div className="border rounded-lg">
              <div className="max-h-[400px] overflow-y-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-background z-10">
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedInfluencers.length === influencers.length && influencers.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedInfluencers(influencers.map(inf => inf.id));
                            } else {
                              setSelectedInfluencers([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Influencer</TableHead>
                      <TableHead>Redes sociais</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Data adicionado</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="max-h-96 overflow-y-auto">
                  {loadingInfluencers ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-muted-foreground">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#9810fa]"></div>
                          <span>Carregando influenciadores...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                                     ) : (() => {
                    const filteredInfluencers = influencers.filter(influencer => 
                      influencer.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      influencer.categoria.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                    
                    console.log('🔍 Filtro aplicado:', {
                      totalInfluencers: influencers.length,
                      searchTerm,
                      filteredCount: filteredInfluencers.length,
                      primeiros3Filtrados: filteredInfluencers.slice(0, 3).map(inf => inf.nome)
                    });
                    
                    return filteredInfluencers;
                  })()
                    .map((influencer) => {
                      const isSelected = selectedInfluencers.includes(influencer.id);
                      
                      return (
                        <TableRow 
                          key={influencer.id}
                          className={`cursor-pointer hover:bg-muted/30 ${isSelected ? 'bg-blue-50' : ''}`}
                          onClick={() => {
                            setSelectedInfluencers(prev => 
                              prev.includes(influencer.id)
                                ? prev.filter(id => id !== influencer.id)
                                : [...prev, influencer.id]
                            );
                          }}
                        >
                          <TableCell>
                            <Checkbox
                              checked={isSelected}
                              onChange={() => {}}
                            />
                          </TableCell>
                          
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                                {influencer.avatar ? (
                                  <img 
                                    src={influencer.avatar} 
                                    alt={influencer.nome}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <User className="h-5 w-5 text-gray-500" />
                                )}
                              </div>
                              <div>
                                <div className="font-medium">{influencer.nome}</div>
                                <div className="text-sm text-muted-foreground">@{influencer.nome.toLowerCase().replace(' ', '')}</div>
                              </div>
                            </div>
                          </TableCell>
                          
                          <TableCell>
                            <div className="flex items-center justify-end gap-2">
                              {influencer.redesSociais.instagram && (
                                <div className="flex items-center gap-1">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                                    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.232s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                                  </svg>
                                  <span className="text-xs text-muted-foreground">
                                    {(influencer.redesSociais.instagram.seguidores / 1000).toFixed(0)}k
                                  </span>
                                </div>
                              )}
                              {influencer.redesSociais.tiktok && (
                                <div className="flex items-center gap-1">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 448 512" className="text-black dark:text-white">
                                    <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                                  </svg>
                                  <span className="text-xs text-muted-foreground">
                                    {(influencer.redesSociais.tiktok.seguidores / 1000).toFixed(0)}k
                                  </span>
                                </div>
                              )}
                              {influencer.redesSociais.youtube && (
                                <div className="flex items-center gap-1">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                                    <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                                  </svg>
                                  <span className="text-xs text-muted-foreground">
                                    {(influencer.redesSociais.youtube.seguidores / 1000).toFixed(0)}k
                                  </span>
                                </div>
                              )}
                              {!influencer.redesSociais.instagram && !influencer.redesSociais.tiktok && !influencer.redesSociais.youtube && (
                                <span className="text-sm text-muted-foreground">-</span>
                              )}
                            </div>
                          </TableCell>
                          
                          <TableCell>
                            <span className="text-sm">
                              {influencer.email || '-'}
                            </span>
                          </TableCell>
                          
                          <TableCell>
                            <span className="text-sm text-muted-foreground">
                              {new Date().toLocaleDateString('pt-BR')}
                            </span>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    
                  {!loadingInfluencers && influencers.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        Nenhum influenciador encontrado
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
                </Table>
              </div>
            </div>

            {/* Contador de selecionados */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{selectedInfluencers.length} selecionados(as)</span>
              <span>{influencers.length} total</span>
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 p-6 border-t bg-muted/30">
            <Button 
              variant="outline" 
              onClick={() => {
                setShowAddInfluencersDialog(false);
                clearModalStates();
              }}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleAddInfluencers}
              className="bg-[#ec003f] hover:bg-[#ec003f]/90 text-white"
              disabled={selectedInfluencers.length === 0}
            >
              Selecionar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog: Selecionar Colunas */}
      <Dialog 
        open={showColumnDialog}
        modal={true}
        onOpenChange={setShowColumnDialog}
      >
        <DialogContent className="max-w-4xl  p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Editar colunas da tabela</DialogTitle>
          </DialogHeader>
          
          <div className="bg-[#9810fa] text-white p-6 rounded-t-lg">
            <h2 className="text-xl font-semibold">Editar Colunas da Tabela</h2>
          </div>

          <div className="p-4 xl:p-6 space-y-3 xl:space-y-4">
            <p className="text-sm text-muted-foreground mb-3 xl:mb-4">
              Selecione quais colunas você deseja exibir na tabela de influenciadores:
            </p>

            {/* 🎯 SEÇÃO DE PRESETS PERSONALIZADOS - SIMPLIFICADA */}
            <div className="flex items-center justify-between mb-4 p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-3">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowCreatePresetModal(true)}
                  className="h-8 px-3 text-xs"
                  title="Salvar configuração atual como preset"
                >
                  <Save className="h-3 w-3" />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 px-3 text-xs"
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      {activePreset ? (
                        <>
                          {columnPresets.find(p => p.id === activePreset)?.name || 'Preset Ativo'}
                          <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                        </>
                      ) : (
                        `Presets (${columnPresets.length})`
                      )}
                      <ChevronDown className="h-3 w-3 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    {columnPresets.length > 0 ? (
                      columnPresets.map((preset) => (
                        <div key={preset.id} className="flex items-center">
                          <DropdownMenuItem
                            onClick={() => applyColumnPreset(preset)}
                            className={`flex-1 cursor-pointer ${
                              activePreset === preset.id
                                ? 'bg-green-50 text-green-700 font-medium'
                                : ''
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              {activePreset === preset.id && (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              )}
                              {preset.name}
                            </div>
                          </DropdownMenuItem>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteColumnPreset(preset.id);
                            }}
                            className="h-6 w-6 p-0 text-red-500 hover:bg-red-50 hover:text-red-600 mr-2"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))
                    ) : (
                      <DropdownMenuItem disabled className="text-muted-foreground text-xs">
                        Nenhum preset salvo
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <p className="text-xs text-muted-foreground">
                Salve e aplique configurações personalizadas
              </p>
            </div>

            <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-2"
                 style={{
                   scrollbarWidth: 'thin',
                   scrollbarColor: '#5600ce30 transparent'
                 }}
            >
              {/* Dados Básicos */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Database className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Dados Básicos</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">  
                      {['category', 'verified'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'category' ? 'Categoria' : 
                             key === 'verified' ? 'Exclusivo' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Localização */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <MapPin className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Localização</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4"> 
                      {['location', 'age', 'gender'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'location' ? 'Localização' : 
                             key === 'age' ? 'Idade' : 
                             key === 'gender' ? 'Gênero' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Plataformas */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Smartphone className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Plataformas</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['plataforma', 'mainNetwork', 'seguidores'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'plataforma' ? 'Plataforma' : 
                             key === 'mainNetwork' ? 'Rede Principal' : 
                             key === 'seguidores' ? 'Seguidores' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Seguidores */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <Heart className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Seguidores</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">   
                      {['instagram_followers', 'youtube_followers', 'tiktok_followers'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'instagram_followers' ? 'Instagram Seguidores' : 
                             key === 'youtube_followers' ? 'YouTube Seguidores' : 
                             key === 'tiktok_followers' ? 'TikTok Seguidores' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Métricas de Engajamento */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Engajamento</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['engagementRate', 'promotesTraders'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'engagementRate' ? 'Engajamento' : 
                             key === 'promotesTraders' ? 'Divulga Trader' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Visualizações */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <Eye className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Visualizações</span>
                </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews', 'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews', 'facebookReelsViews', 'twitchViews', 'kwaiViews'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'tiktok_views' ? 'TikTok Views' : 
                             key === 'youtube_views' ? 'YouTube Views' : 
                             key === 'instagramStoriesViews' ? 'Instagram Stories Views' : 
                             key === 'instagramReelsViews' ? 'Instagram Reels Views' : 
                             key === 'tiktokVideoViews' ? 'TikTok Video Views' : 
                             key === 'youtubeShortsViews' ? 'YouTube Shorts Views' : 
                             key === 'youtubeLongFormViews' ? 'YouTube Views Videos Longos' : 
                             key === 'facebookStoriesViews' ? 'Facebook Stories Views' : 
                             key === 'facebookReelsViews' ? 'Facebook Reels Views' : 
                             key === 'twitchViews' ? 'Twitch Views' : 
                             key === 'kwaiViews' ? 'Kwai Views' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Contato */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Phone className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Contato</span>
                </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4">
                      {['responsibleInfo', 'contato'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'responsibleInfo' ? 'Responsável' : 
                             key === 'contato' ? 'Contato' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Serviços */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#ff0074]/10 flex items-center justify-center">
                      <Briefcase className="h-4 w-4 text-[#ff0074]" />
                    </div>
                    <span className="text-sm font-semibold text-[#ff0074]">Serviços</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                                    <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                      <div className="flex flex-wrap gap-4">
                        {['instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video', 'youtube_shorts', 'tiktok_video', 'total_budget'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            {key === 'instagram_story' ? 'Stories' : 
                             key === 'instagram_reel' ? 'Reels' : 
                             key === 'youtube_insertion' ? 'Inserção' : 
                             key === 'youtube_video' ? 'Dedicado' : 
                             key === 'youtube_shorts' ? 'Shorts' : 
                             key === 'tiktok_video' ? 'TikTok' : 
                             key === 'total_budget' ? 'Total' : 
                             columnLabels[key]}
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>

              {/* Status */}
              <Collapsible>
                <CollapsibleTrigger className="w-full flex items-center justify-between p-3 bg-muted/30 hover:bg-muted/50 rounded-lg border transition-all duration-200 hover:shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-[#5600ce]/10 flex items-center justify-center">
                      <Activity className="h-4 w-4 text-[#5600ce]" />
                    </div>
                    <span className="text-sm font-semibold text-[#5600ce]">Status</span>
                  </div>
                  <ChevronDown className="h-4 w-4 transition-transform [&[data-state=open]]:rotate-180" />
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="px-3 pb-3"
                  >
                    <div className="flex flex-wrap gap-4"> 
                      {['status'].map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={visibleColumns[key] !== false}
                        onCheckedChange={() => toggleColumnVisibility(key)}
                        className="h-4 w-4"
                      />
                      <label
                        htmlFor={key}
                        className="text-xs xl:text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        style={{ color: TEXT_COLOR }}
                      >
                            Status
                      </label>
                    </div>
                  ))}
                </div>
                  </motion.div>
                </CollapsibleContent>
              </Collapsible>
            </div>

            <div className="flex items-center justify-between pt-3 xl:pt-2 border-t">
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-[#5600ce]/10 text-[#5600ce] text-xs font-medium">
                  <span>{Object.values(visibleColumns).filter(Boolean).length}</span>
                  <span>/</span>
                  <span>32</span>
                </span>
                <span className="hidden sm:inline">colunas selecionadas</span>
              </div>
              
              <div className="flex gap-1 xl:gap-1.5 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const allowedColumns = [
                      'category', 'verified', 'status', 'location', 'age', 'gender', 
                      'plataforma', 'mainNetwork', 'seguidores', 'instagram_followers', 
                      'youtube_followers', 'tiktok_followers', 'engagementRate', 'promotesTraders',
                      'tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews', 
                      'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews', 
                      'facebookReelsViews', 'twitchViews', 'kwaiViews', 'responsibleInfo', 'contato', 
                      'instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video', 
                      'youtube_shorts', 'tiktok_video', 'total_budget'
                    ];
                    const allVisible = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = allowedColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(allVisible);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(allVisible, columnOrder);
                  }}
                >
                  Todas
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const essentialColumns = ['category', 'verified'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = essentialColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Essenciais
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#5600ce] border-[#5600ce]/30 hover:bg-[#5600ce]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const basicColumns = ['location', 'age', 'gender', 'category', 'verified'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = basicColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Básicos
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#ff0074] border-[#ff0074]/30 hover:bg-[#ff0074]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const socialColumns = ['instagram_followers', 'youtube_followers', 'tiktok_followers', 'engagementRate'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = socialColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Social
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#270038] dark:text-white border hover:bg-[#270038]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const viewsColumns = ['tiktok_views', 'youtube_views', 'instagramStoriesViews', 'instagramReelsViews', 'tiktokVideoViews', 'youtubeShortsViews', 'youtubeLongFormViews', 'facebookStoriesViews', 'facebookReelsViews', 'twitchViews', 'kwaiViews'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = viewsColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Views
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#5600ce] border-[#5600ce]/30 hover:bg-[#5600ce]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const serviceColumns = ['instagram_story', 'instagram_reel', 'youtube_insertion', 'youtube_video', 'youtube_shorts', 'tiktok_video', 'total_budget'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = serviceColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Serviços
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#ff0074] border-[#ff0074]/30 hover:bg-[#ff0074]/10 text-xs xl:text-sm h-7 xl:h-8 px-2 xl:px-3"
                  onClick={() => {
                    const contactColumns = ['responsibleInfo', 'contato'];
                    const newVisibility = Object.keys(columnLabels).reduce((acc, key) => {
                      acc[key] = contactColumns.includes(key);
                      return acc;
                    }, {} as { [key: string]: boolean });
                    setVisibleColumns(newVisibility);
                    setActivePreset(null); // 🎯 LIMPAR PRESET ATIVO
                    saveColumnSettingsToDb(newVisibility, columnOrder);
                  }}
                >
                  Contato
                </Button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 xl:gap-3 p-4 xl:p-6 border-t bg-muted/30">
            <Button 
              variant="outline" 
              size="sm"
              className="h-8 xl:h-9 px-3 xl:px-4 text-sm"
              onClick={() => setShowColumnDialog(false)}
            >
              Cancelar
            </Button>
            <Button 
              size="sm"
              className="bg-[#ec003f] hover:bg-[#ec003f]/90 text-white h-8 xl:h-9 px-3 xl:px-4 text-sm"
              onClick={() => setShowColumnDialog(false)}
            >
              Aplicar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog: Criar Preset Personalizado */}
      <Dialog
        open={showCreatePresetModal}
        modal={true}
        onOpenChange={setShowCreatePresetModal}
      >
        <DialogContent className="max-w-md p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Criar preset personalizado</DialogTitle>
          </DialogHeader>

          <div className="bg-gradient-to-r from-[#ff0074] to-[#5600ce] text-white p-6 rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <Save className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-lg font-semibold">Criar Preset</h2>
                <p className="text-sm opacity-90">Salve a configuração atual das colunas</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="preset-name" className="text-sm font-medium">
                Nome do Preset
              </Label>
              <Input
                id="preset-name"
                type="text"
                value={presetName}
                onChange={(e) => setPresetName(e.target.value)}
                placeholder="Ex: Análise Completa, Setup Marketing..."
                className="w-full"
                autoFocus
                disabled={isSavingPreset}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && presetName.trim() && !isSavingPreset) {
                    e.preventDefault();
                    saveColumnPreset();
                  }
                }}
              />
              <p className="text-xs text-muted-foreground">
                Escolha um nome descritivo para identificar facilmente este preset
              </p>
            </div>

            <div className="bg-muted/30 rounded-lg p-3">
              <p className="text-xs font-medium text-muted-foreground mb-2">Configuração atual:</p>
              <p className="text-xs text-muted-foreground">
                {Object.values(visibleColumns).filter(Boolean).length} de {Object.keys(visibleColumns).length} colunas selecionadas
              </p>
            </div>
          </div>

          <div className="flex items-center justify-end gap-3 p-6 border-t bg-muted/20">
            <Button
              variant="outline"
              onClick={() => {
                setShowCreatePresetModal(false);
                setPresetName('');
              }}
              disabled={isSavingPreset}
            >
              Cancelar
            </Button>
            <Button
              onClick={saveColumnPreset}
              className="bg-gradient-to-r from-[#ff0074] to-[#5600ce] hover:from-[#ff0074]/90 hover:to-[#5600ce]/90 text-white"
              disabled={!presetName.trim() || isSavingPreset}
            >
              {isSavingPreset ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Criar Preset
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>



      {/* Dialog: Editar Orçamento */}
      <Dialog 
        open={showBudgetDialog}
        modal={true}
        onOpenChange={setShowBudgetDialog}
      >
        <DialogContent className="max-w-md p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Editar orçamento</DialogTitle>
          </DialogHeader>
          
          <div className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] text-white p-6 rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 dark:bg-[#08050f] rounded-full flex items-center justify-center">
                <DollarSign className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-lg font-semibold">Definir Orçamento</h2>
                <p className="text-sm opacity-90">{budgetEditData.serviceName}</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6 dark:bg-[#08050f]">
            {/* Informações do Influenciador */}
            <div className="flex items-center gap-3 p-4 dark:bg-[#08050f] bg-muted/30 rounded-lg">
              <div className="w-12 h-12 bg-gradient-to-br from-[#ec003f] to-[#9810fa] rounded-full flex items-center justify-center text-white font-bold overflow-hidden">
                {(() => {
                  const perfil = proposta?.perfis.find(p => p.id === budgetEditData.profileId);
                  const influencerData = getInfluencerData(perfil?.influencerId || '');
                  return influencerData?.avatar ? (
                    <img 
                      src={influencerData.avatar} 
                      alt={budgetEditData.influencerName}
                      className="w-full h-full object-cover"
                    />
                  ) : budgetEditData.influencerName.charAt(0).toUpperCase();
                })()}
              </div>
              <div>
                <p className="font-medium">{budgetEditData.influencerName}</p>
                <p className="text-sm text-muted-foreground">
                  Serviço: {budgetEditData.serviceName}
                </p>
              </div>
            </div>

            {/* Preço Original */}
            {budgetEditData.originalPrice > 0 && (
              <div className="p-4 bg-gray-50 dark:bg-[#08050f] rounded-lg border">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Preço Original:</span>
                  <span className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                    {formatCurrency(budgetEditData.originalPrice)}
                  </span>
                </div>
              </div>
            )}

            {/* Campo de Quantidade */}
            <div className="space-y-3">
              <Label htmlFor="quantity-value" className="text-xs font-medium">
                Quantidade de Serviços
              </Label>
              
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                  Qtd:
                </div>
                <Input
                  id="quantity-value"
                  type="number"
                  min="1"
                  value={quantityInputValue}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseInt(value) >= 1) {
                      setQuantityInputValue(value);
                    }
                  }}
                  placeholder="1"
                  className="pl-14 text-lg font-medium"
                  disabled={isSavingBudget}
                />
              </div>
              
              <div className="text-xs text-muted-foreground">
                Quantos serviços deste tipo (ex: 2 stories, 3 posts dedicados)
              </div>
            </div>

            {/* Campo de Valor */}
            <div className="space-y-3">
              <Label htmlFor="budget-value" className="text-xs font-medium">
                Valor Unitário
              </Label>
              
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                  R$
                </div>
                <Input
                  id="budget-value"
                  type="text"
                  value={budgetInputValue}
                  onChange={(e) => {
                    // Permite apenas números, vírgulas e pontos
                    const value = e.target.value.replace(/[^\d,.-]/g, '');
                    setBudgetInputValue(value);
                  }}
                  onKeyDown={handleBudgetInputKeyDown}
                  placeholder="0,00"
                  className="pl-12 text-lg font-medium"
                  autoFocus
                  disabled={isSavingBudget}
                />
              </div>
              
              <div className="text-xs text-muted-foreground">
                Digite o valor unitário em reais (ex: 1000,00 ou 1000.00)
              </div>
            </div>

            {/* Valor Total Calculado */}
            {(() => {
              const unitValue = parseFloat(budgetInputValue.replace(/[^\d,.-]/g, '').replace(',', '.'));
              const quantity = parseInt(quantityInputValue) || 1;
              const totalValue = !isNaN(unitValue) && unitValue > 0 ? unitValue * quantity : 0;
              
              if (totalValue > 0) {
                return (
                  <div className="p-4 bg-gradient-to-r from-[#ec003f]/10 to-[#9810fa]/10 dark:from-[#ec003f]/20 dark:to-[#9810fa]/20 rounded-lg border border-[#ec003f]/20">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Valor Total:</span>
                      <div className="text-right">
                        <div className="text-lg font-bold text-[#ec003f]">
                          {formatCurrency(totalValue)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {quantity}x {formatCurrency(unitValue)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
              
              return null;
            })()}

            {/* Análise de Diferença e Porcentagem */}
            {(() => {
              const inputValue = parseFloat(budgetInputValue.replace(/[^\d,.-]/g, '').replace(',', '.'));
              const hasValidInput = !isNaN(inputValue) && inputValue > 0;
              const originalPrice = budgetEditData.originalPrice;
              
              if (hasValidInput && originalPrice > 0) {
                const difference = inputValue - originalPrice;
                const percentage = ((difference / originalPrice) * 100);
                const isHigher = difference > 0;
                const isLower = difference < 0;
                
                return (
                  <div className="space-y-3">
                    {/* Diferença */}
                    <div className={`p-3 rounded-lg ${
                      isHigher ? 'bg-green-50 dark:bg-green-950/30' :
                      isLower ? 'bg-blue-50 dark:bg-blue-950/30' :
                      'bg-gray-50 dark:bg-gray-900/30'
                    }`}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Diferença</span>
                        <span className={`text-sm font-medium ${
                          isHigher ? 'text-green-600 dark:text-green-400' :
                          isLower ? 'text-blue-600 dark:text-blue-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                          {difference > 0 ? '+' : ''}{formatCurrency(Math.abs(difference))}
                        </span>
                      </div>
                    </div>

                    {/* Porcentagem */}
                    <div className={`p-3 rounded-lg ${
                      isHigher ? 'bg-green-50 dark:bg-green-950/30' :
                      isLower ? 'bg-blue-50 dark:bg-blue-950/30' :
                      'bg-gray-50 dark:bg-gray-900/30'
                    }`}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Variação</span>
                        <span className={`text-sm font-medium ${
                          isHigher ? 'text-green-600 dark:text-green-400' :
                          isLower ? 'text-blue-600 dark:text-blue-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                          {percentage > 0 ? '+' : ''}{percentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    {/* Resumo simplificado */}
                    {(isHigher || isLower) && (
                      <div className="text-center">
                        <p className={`text-xs ${
                          isHigher ? 'text-green-600 dark:text-green-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {isHigher && `${percentage.toFixed(1)}% acima do original`}
                          {isLower && `${Math.abs(percentage).toFixed(1)}% de desconto`}
                        </p>
                      </div>
                    )}
                  </div>
                );
              }
              
              return null;
            })()}

           
          </div>

          <div className="flex items-center justify-end gap-3 p-6 border-t dark:bg-[#08050f] bg-muted/20">
            <Button 
              variant="outline" 
              onClick={() => {
                setShowBudgetDialog(false);
                setBudgetInputValue('');
              }}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSaveBudget}
              className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90 text-white px-6"
              disabled={!budgetInputValue.trim() || isSavingBudget}
            >
              {isSavingBudget ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Salvar Orçamento
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog: Configurações de Compartilhamento */}
      <Dialog 
        open={showShareConfigDialog}
        modal={true}
        onOpenChange={setShowShareConfigDialog}
      >
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-[#ec003f]" />
              Configurar Compartilhamento
            </DialogTitle>
            <p className="text-sm text-muted-foreground">
              Configure quais informações incluir no snapshot da proposta
            </p>
          </DialogHeader>

          <div className="space-y-6">
            {/* Configurações de Acesso */}
            <div className="space-y-3">
              <h3 className="font-semibold text-base flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Configurações de Acesso
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Tipo de Acesso</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="config-public"
                        name="configAccessType"
                        checked={shareSettings.isPublic}
                        onChange={() => setShareSettings(prev => ({ ...prev, isPublic: true, requiresPassword: false }))}
                        className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                      />
                      <Label htmlFor="config-public" className="text-sm cursor-pointer">
                        Público (qualquer pessoa com link)
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="config-private"
                        name="configAccessType"
                        checked={!shareSettings.isPublic}
                        onChange={() => setShareSettings(prev => ({ ...prev, isPublic: false }))}
                        className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                      />
                      <Label htmlFor="config-private" className="text-sm cursor-pointer">
                        Privado (acesso restrito)
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Permissões</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="config-view"
                        name="configPermission"
                        checked={!shareSettings.allowDownload}
                        onChange={() => setShareSettings(prev => ({ ...prev, allowDownload: false }))}
                        className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                      />
                      <Label htmlFor="config-view" className="text-sm cursor-pointer">
                        Apenas visualizar
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="config-download"
                        name="configPermission"
                        checked={shareSettings.allowDownload}
                        onChange={() => setShareSettings(prev => ({ ...prev, allowDownload: true }))}
                        className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                      />
                      <Label htmlFor="config-download" className="text-sm cursor-pointer">
                        Visualizar e baixar
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Campos a Incluir */}
            <div className="space-y-3">
              <h3 className="font-semibold text-base flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Informações a Incluir
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-personal"
                      checked={shareSettings.includeFields.personalInfo}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, personalInfo: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-personal" className="text-sm cursor-pointer">
                      Informações Pessoais
                    </Label>
                  </div>
                  <p className="text-xs text-left text-muted-foreground ml-6">Nome, idade, localização</p>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-social"
                      checked={shareSettings.includeFields.socialStats}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, socialStats: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-social" className="text-sm cursor-pointer">
                      Estatísticas Sociais
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">Seguidores, engagement, views</p>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-pricing"
                      checked={shareSettings.includeFields.pricing}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, pricing: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-pricing" className="text-sm cursor-pointer">
                      Tabela de Preços
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">Valores dos serviços</p>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-budgets"
                      checked={shareSettings.includeFields.budgets}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, budgets: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-budgets" className="text-sm cursor-pointer">
                      Orçamentos da Proposta
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">Valores negociados</p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-demographics"
                      checked={shareSettings.includeFields.demographics}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, demographics: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-demographics" className="text-sm cursor-pointer">
                      Dados Demográficos
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">Audiência por gênero/idade</p>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-contact"
                      checked={shareSettings.includeFields.contactInfo}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, contactInfo: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-contact" className="text-sm cursor-pointer">
                      Informações de Contato
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">WhatsApp, email, responsável</p>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="include-comments"
                      checked={shareSettings.includeFields.comments}
                      onChange={(e) => setShareSettings(prev => ({
                        ...prev,
                        includeFields: { ...prev.includeFields, comments: e.target.checked }
                      }))}
                      className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                    />
                    <Label htmlFor="include-comments" className="text-sm cursor-pointer">
                      Comentários Internos
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">Observações da equipe</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Personalização */}
            <div className="space-y-3">
              <h3 className="font-semibold text-base flex items-center gap-2">
                <Paintbrush className="h-4 w-4" />
                Personalização
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="include-logo"
                    checked={shareSettings.branding.includeLogo}
                    onChange={(e) => setShareSettings(prev => ({
                      ...prev,
                      branding: { ...prev.branding, includeLogo: e.target.checked }
                    }))}
                    className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f] rounded"
                  />
                  <Label htmlFor="include-logo" className="text-sm cursor-pointer">
                    Incluir logo da empresa
                  </Label>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Mensagem personalizada (opcional)</Label>
                  <textarea
                    value={shareSettings.branding.customMessage}
                    onChange={(e) => setShareSettings(prev => ({
                      ...prev,
                      branding: { ...prev.branding, customMessage: e.target.value }
                    }))}
                    placeholder="Ex: Proposta comercial exclusiva para..."
                    className="w-full p-2 border rounded-md text-sm resize-none"
                    rows={2}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={() => setShowShareConfigDialog(false)}
            >
              Cancelar
            </Button>
            
            <Button 
              onClick={handleCreateSnapshot}
              disabled={sharingLoading}
              className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90 text-white px-6"
            >
              {sharingLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Criando snapshot...
                </>
              ) : (
                <>
                  <Share className="h-4 w-4 mr-2" />
                  Criar Snapshot
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Sheet: Gerenciar Permissões por Email */}
      {params?.id && (
        <EmailPermissionsManager
          proposalId={Array.isArray(params.id) ? params.id[0] : params.id}
          proposalName={proposta?.nome}
          open={showEmailPermissionsDialog}
          onOpenChange={setShowEmailPermissionsDialog}
        />
      )}

      {/* Dialog: Compartilhar Proposta */}
      <Dialog 
        open={showShareDialog}
        modal={true}
        onOpenChange={setShowShareDialog}
      >
        <DialogContent className="max-w-sm p-0">
          <DialogHeader className="sr-only">
            <DialogTitle>Compartilhar proposta</DialogTitle>
          </DialogHeader>
          
          <div className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] text-white p-4 rounded-t-lg">
            <div className="flex items-center gap-2">
              <Share className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Compartilhar</h2>
            </div>
          </div>

          <div className="p-4 space-y-4">
            <div className="flex gap-2">
              <Input
                value={shareUrl}
                readOnly
                className="flex-1 bg-gray-50 dark:bg-gray-900/50 text-sm"
                onClick={(e) => (e.target as HTMLInputElement).select()}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(shareUrl);
                  toast({
                    title: "Link copiado!",
                  });
                }}
                className="px-3"
              >
                Copiar
              </Button>
            </div>

            {/* Configurações simples */}
            <div className="space-y-3">
              {/* Tipo de Acesso */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="public"
                    name="accessType"
                    checked={shareSettings.isPublic}
                    onChange={() => setShareSettings(prev => ({ ...prev, isPublic: true, requiresPassword: false }))}
                    className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                  />
                  <Label htmlFor="public" className="text-sm cursor-pointer font-medium">
                    Público
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="private"
                    name="accessType"
                    checked={!shareSettings.isPublic}
                    onChange={() => setShareSettings(prev => ({ ...prev, isPublic: false }))}
                    className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                  />
                  <Label htmlFor="private" className="text-sm cursor-pointer font-medium">
                    Privado
                  </Label>
                </div>
              </div>

              {/* Permissões */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="view"
                    name="permission"
                    checked={!shareSettings.allowDownload}
                    onChange={() => setShareSettings(prev => ({ ...prev, allowDownload: false }))}
                    className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                  />
                  <Label htmlFor="view" className="text-sm cursor-pointer font-medium">
                    Visualizar
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="edit"
                    name="permission"
                    checked={shareSettings.allowDownload}
                    onChange={() => setShareSettings(prev => ({ ...prev, allowDownload: true }))}
                    className="w-4 h-4 text-[#ec003f] focus:ring-[#ec003f]"
                  />
                  <Label htmlFor="edit" className="text-sm cursor-pointer font-medium">
                    Editar
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 p-4 border-t bg-muted/20">
            <Button 
              variant="outline" 
              onClick={() => setShowShareDialog(false)}
              size="sm"
            >
              Fechar
            </Button>
            <Button 
              onClick={() => {
                navigator.clipboard.writeText(shareUrl);
                toast({
                  title: "Link copiado!",
                });
                setShowShareDialog(false);
              }}
              className="bg-gradient-to-r from-[#ec003f] to-[#9810fa] hover:from-[#ec003f]/90 hover:to-[#9810fa]/90 text-white"
              size="sm"
            >
              Copiar e Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 🆕 MODAL DE CRIAÇÃO DE PACOTES */}
      <Dialog open={showPackageDialog} onOpenChange={setShowPackageDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-[#5600ce]">
              {packageEditData?.isEditing ? 'Editar Pacote' : 'Criar Novo Pacote'}
            </DialogTitle>
            <p className="text-sm text-muted-foreground">
              {packageEditData?.influencerName && (
                <>Para: <span className="font-medium">{packageEditData.influencerName}</span></>
              )}
            </p>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informações básicas do pacote */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="package-name">Nome do Pacote</Label>
                <Input
                  id="package-name"
                  placeholder="Ex: Pacote Instagram Básico"
                  value={packageFormData.name}
                  onChange={(e) => setPackageFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="package-category">Categoria</Label>
                <Input
                  id="package-category"
                  placeholder="Ex: Instagram, YouTube, Completo"
                  value={packageFormData.category}
                  onChange={(e) => setPackageFormData(prev => ({ ...prev, category: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="package-description">Descrição (opcional)</Label>
              <textarea
                id="package-description"
                className="w-full p-3 border rounded-md resize-none"
                rows={3}
                placeholder="Descreva o que está incluído neste pacote..."
                value={packageFormData.description}
                onChange={(e) => setPackageFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            {/* Seleção de serviços */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Serviços Incluídos</Label>
                <span className="text-sm text-muted-foreground">
                  {selectedPackageServices.length} serviço(s) selecionado(s)
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { type: 'instagram_story', name: 'Instagram Stories', icon: '📱' },
                  { type: 'instagram_reel', name: 'Instagram Reels', icon: '🎬' },
                  { type: 'youtube_video', name: 'YouTube Vídeo', icon: '📺' },
                  { type: 'youtube_shorts', name: 'YouTube Shorts', icon: '⚡' },
                  { type: 'tiktok_video', name: 'TikTok Vídeo', icon: '🎵' },
                  { type: 'youtube_insertion', name: 'YouTube Inserção', icon: '📽️' }
                ].map((service) => {
                  const isSelected = selectedPackageServices.some(s => s.serviceType === service.type);
                  const selectedService = selectedPackageServices.find(s => s.serviceType === service.type);

                  return (
                    <div
                      key={service.type}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        isSelected
                          ? 'border-[#5600ce] bg-[#5600ce]/5'
                          : 'border-muted hover:border-[#5600ce]/50'
                      }`}
                      onClick={() => {
                        if (isSelected) {
                          setSelectedPackageServices(prev =>
                            prev.filter(s => s.serviceType !== service.type)
                          );
                        } else {
                          setSelectedPackageServices(prev => [
                            ...prev,
                            {
                              serviceType: service.type as any,
                              quantity: 1,
                              serviceName: service.name
                            }
                          ]);
                        }
                      }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{service.icon}</span>
                          <span className="font-medium text-sm">{service.name}</span>
                        </div>
                        {isSelected && (
                          <Check className="h-4 w-4 text-[#5600ce]" />
                        )}
                      </div>

                      {isSelected && (
                        <div className="mt-2">
                          <Label className="text-xs">Quantidade</Label>
                          <Input
                            type="number"
                            min="1"
                            value={selectedService?.quantity || 1}
                            onChange={(e) => {
                              const newQuantity = parseInt(e.target.value) || 1;
                              setSelectedPackageServices(prev =>
                                prev.map(s =>
                                  s.serviceType === service.type
                                    ? { ...s, quantity: newQuantity }
                                    : s
                                )
                              );
                            }}
                            className="mt-1 h-8"
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Preço total */}
            <div className="space-y-2">
              <Label htmlFor="package-price">Preço Total do Pacote</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                  R$
                </span>
                <Input
                  id="package-price"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0,00"
                  className="pl-10"
                  value={packageFormData.totalPrice}
                  onChange={(e) => setPackageFormData(prev => ({
                    ...prev,
                    totalPrice: parseFloat(e.target.value) || 0
                  }))}
                />
              </div>
            </div>

            {/* Opções adicionais */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="save-as-template"
                checked={packageFormData.isTemplate}
                onChange={(e) => setPackageFormData(prev => ({
                  ...prev,
                  isTemplate: e.target.checked
                }))}
              />
              <Label htmlFor="save-as-template" className="text-sm cursor-pointer">
                Salvar como template para reutilização
              </Label>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => {
                setShowPackageDialog(false);
                setPackageEditData(null);
                setSelectedPackageServices([]);
                setPackageFormData({
                  name: '',
                  description: '',
                  totalPrice: 0,
                  category: '',
                  isTemplate: false
                });
              }}
            >
              Cancelar
            </Button>
            <Button
              onClick={() => {
                // TODO: Implementar lógica de salvamento
                console.log('Salvando pacote:', {
                  ...packageFormData,
                  services: selectedPackageServices,
                  influencerId: packageEditData?.influencerId
                });

                // Fechar modal
                setShowPackageDialog(false);
                setPackageEditData(null);
                setSelectedPackageServices([]);
                setPackageFormData({
                  name: '',
                  description: '',
                  totalPrice: 0,
                  category: '',
                  isTemplate: false
                });

                // Mostrar toast de sucesso
                toast({
                  title: "Pacote criado com sucesso!",
                  description: `${packageFormData.name} foi adicionado à proposta.`,
                });
              }}
              disabled={!packageFormData.name || selectedPackageServices.length === 0 || packageFormData.totalPrice <= 0}
              className="bg-gradient-to-r from-[#5600ce] to-[#ff0074] text-white hover:opacity-90"
            >
              {packageEditData?.isEditing ? 'Salvar Alterações' : 'Criar Pacote'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 