import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { ServicePackage, PackageService } from '@/types/service-package';
import { ServiceType } from '@/types/budget';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [POST] Recebendo dados do pacote de serviços');

    let body: {
      name: string;
      description?: string;
      totalPrice: number;
      isTemplate?: boolean;
      category?: string;
      tags?: string[];
      minFollowers?: number;
      platforms: string[];
      services: PackageService[];
      influencerId: string;
      influencerName: string;
      proposalId: string;
      userId: string;
      brandId: string;
    };

    try {
      body = await request.json();
      console.log('📦 [POST] Body parseado com sucesso');
    } catch (parseError) {
      console.error('❌ [POST] Erro ao parsear JSON:', parseError);
      return NextResponse.json(
        { error: 'Dados JSON inválidos' },
        { status: 400 }
      );
    }
    
    console.log('📦 [POST] Dados recebidos:', {
      name: body.name,
      influencerId: body.influencerId,
      proposalId: body.proposalId,
      servicesCount: body.services?.length || 0,
      userId: body.userId,
      brandId: body.brandId,
      totalPrice: body.totalPrice,
      platforms: body.platforms
    });

    // Log detalhado dos serviços
    if (body.services && body.services.length > 0) {
      console.log('🔧 [POST] Serviços detalhados:');
      body.services.forEach((service, index) => {
        console.log(`  ${index + 1}. ${service.serviceName} (${service.platform}): ${service.quantity}x R$${service.unitPrice} = R$${service.totalPrice}`);
      });
    }

    console.log('🔍 [POST] Iniciando validações...');

    // Validação de campos obrigatórios
    if (!body.name?.trim()) {
      console.log('❌ [POST] Nome do pacote não fornecido');
      return NextResponse.json(
        { error: 'Nome do pacote é obrigatório' },
        { status: 400 }
      );
    }

    if (!body.influencerId || !body.proposalId || !body.userId || !body.brandId) {
      return NextResponse.json(
        { error: 'Campos obrigatórios: influencerId, proposalId, userId, brandId' },
        { status: 400 }
      );
    }

    if (!body.services || body.services.length === 0) {
      return NextResponse.json(
        { error: 'Pelo menos um serviço deve ser selecionado' },
        { status: 400 }
      );
    }

    // Validar estrutura dos serviços
    console.log('🔍 [POST] Validando estrutura dos serviços...');
    for (let i = 0; i < body.services.length; i++) {
      const service = body.services[i];
      console.log(`🔧 [POST] Validando serviço ${i + 1}:`, {
        serviceType: service.serviceType,
        platform: service.platform,
        serviceName: service.serviceName,
        quantity: service.quantity,
        unitPrice: service.unitPrice,
        totalPrice: service.totalPrice
      });

      if (!service.serviceType || !service.platform) {
        console.log(`❌ [POST] Serviço ${i + 1} inválido - faltam campos obrigatórios`);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: serviceType e platform são obrigatórios` },
          { status: 400 }
        );
      }

      if (typeof service.quantity !== 'number' || service.quantity <= 0) {
        console.log(`❌ [POST] Serviço ${i + 1} - quantidade inválida:`, service.quantity);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Quantidade deve ser um número maior que 0` },
          { status: 400 }
        );
      }

      if (typeof service.unitPrice !== 'number' || service.unitPrice < 0) {
        console.log(`❌ [POST] Serviço ${i + 1} - preço unitário inválido:`, service.unitPrice);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Preço unitário deve ser um número não negativo` },
          { status: 400 }
        );
      }

      // Validar se totalPrice está correto
      const expectedTotal = service.quantity * service.unitPrice;
      if (typeof service.totalPrice !== 'number' || Math.abs(service.totalPrice - expectedTotal) > 0.01) {
        console.log(`❌ [POST] Serviço ${i + 1} - preço total incorreto. Esperado: ${expectedTotal}, Recebido: ${service.totalPrice}`);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Preço total incorreto. Esperado: R$${expectedTotal.toFixed(2)}` },
          { status: 400 }
        );
      }
    }
    console.log('✅ [POST] Todos os serviços validados com sucesso');

    // Calcular preço total do pacote
    const calculatedTotalPrice = body.services.reduce((total, service) => total + service.totalPrice, 0);
    
    if (Math.abs(body.totalPrice - calculatedTotalPrice) > 0.01) {
      return NextResponse.json(
        { error: 'Preço total do pacote não confere com a soma dos serviços' },
        { status: 400 }
      );
    }

    console.log('🔍 [POST] Verificando proposta:', body.proposalId);

    // Verificar se a proposta existe e o usuário tem acesso
    let proposalDoc;
    try {
      proposalDoc = await db.collection('proposals').doc(body.proposalId).get();
      console.log('📄 [POST] Proposta encontrada:', proposalDoc.exists);
    } catch (dbError) {
      console.error('❌ [POST] Erro ao acessar Firebase:', dbError);
      return NextResponse.json(
        { error: 'Erro ao acessar banco de dados' },
        { status: 500 }
      );
    }

    if (!proposalDoc.exists) {
      console.log('❌ [POST] Proposta não encontrada:', body.proposalId);
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    console.log('📄 [POST] Dados da proposta carregados, owner:', proposalData?.criadoPor);
    
    // Verificar permissões (owner ou colaborador)
    const isOwner = proposalData?.criadoPor === body.userId;
    let isCollaborator = false;

    console.log('🔐 [POST] Verificando permissões:', {
      userId: body.userId,
      proposalOwner: proposalData?.criadoPor,
      isOwner
    });

    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) =>
        collab.email === body.userId || collab.userId === body.userId
      );
      console.log('👥 [POST] Verificação de colaborador:', { isCollaborator, collaboratorsCount: proposalData.collaborators.length });
    }

    if (!isOwner && !isCollaborator) {
      console.log('❌ [POST] Acesso negado - usuário sem permissão');
      return NextResponse.json(
        { error: 'Acesso negado à proposta' },
        { status: 403 }
      );
    }

    console.log('✅ [POST] Permissões verificadas com sucesso');

    // Verificar se o influenciador existe na proposta
    console.log('👤 [POST] Verificando se influenciador existe na proposta...');
    try {
      const influencerDoc = await db
        .collection('proposals')
        .doc(body.proposalId)
        .collection('influencers')
        .doc(body.influencerId)
        .get();

      if (!influencerDoc.exists) {
        console.log('❌ [POST] Influenciador não encontrado na proposta');
        return NextResponse.json(
          { error: 'Influenciador não encontrado na proposta' },
          { status: 404 }
        );
      }
      console.log('✅ [POST] Influenciador encontrado na proposta');
    } catch (influencerError) {
      console.error('❌ [POST] Erro ao verificar influenciador:', influencerError);
      return NextResponse.json(
        { error: 'Erro ao verificar influenciador na proposta' },
        { status: 500 }
      );
    }

    console.log('🔧 [POST] Preparando dados do pacote...');

    // Preparar dados do pacote para salvar na subcoleção budgets
    const packageData: any = {
      // Identificação
      type: 'package', // Diferencia de budgets individuais
      packageName: body.name.trim(),
      description: body.description || '',

      // Relacionamentos
      influencerId: body.influencerId,
      influencerName: body.influencerName || '',
      userId: body.userId,
      brandId: body.brandId,
      proposalId: body.proposalId,

      // Dados financeiros
      amount: body.totalPrice,
      currency: 'BRL',

      // Serviços do pacote - limpar campos undefined
      services: body.services.map(service => ({
        serviceType: service.serviceType,
        quantity: service.quantity,
        unitPrice: service.unitPrice,
        totalPrice: service.totalPrice,
        serviceName: service.serviceName || '',
        platform: service.platform,
        originalPrice: service.originalPrice || service.unitPrice
      })),
      platforms: body.platforms || [...new Set(body.services.map(s => s.platform))],

      // Configurações do pacote - garantir que não há undefined
      isTemplate: body.isTemplate === true,
      category: body.category || '',
      tags: body.tags || [],

      // Status e workflow
      status: 'draft',
      serviceType: 'package',

      // Contrapropostas (seguindo padrão dos budgets)
      counterProposals: [],
      hasCounterProposal: false,

      // Metadados
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.userId
    };

    // Adicionar campos opcionais apenas se tiverem valor
    if (body.minFollowers && body.minFollowers > 0) {
      packageData.minFollowers = body.minFollowers;
    }

    console.log('📋 [POST] Dados preparados:', {
      type: packageData.type,
      packageName: packageData.packageName,
      servicesCount: packageData.services.length,
      amount: packageData.amount,
      hasUndefinedFields: Object.values(packageData).some(v => v === undefined)
    });

    console.log('💾 [POST] Salvando pacote na estrutura hierárquica:', {
      proposalId: body.proposalId,
      influencerId: body.influencerId,
      packageName: body.name
    });

    // Salvar na mesma estrutura dos budgets: proposals/{proposalId}/influencers/{influencerId}/budgets/{packageId}
    let packageRef;
    try {
      console.log('💾 [POST] Iniciando salvamento no Firebase...');

      // Validar se todos os campos obrigatórios estão presentes
      const requiredFields = ['type', 'packageName', 'influencerId', 'userId', 'brandId', 'amount', 'services'];
      const missingFields = requiredFields.filter(field => !packageData[field]);

      if (missingFields.length > 0) {
        console.error('❌ [POST] Campos obrigatórios faltando:', missingFields);
        return NextResponse.json(
          { error: `Campos obrigatórios faltando: ${missingFields.join(', ')}` },
          { status: 400 }
        );
      }

      packageRef = await db
        .collection('proposals')
        .doc(body.proposalId)
        .collection('influencers')
        .doc(body.influencerId)
        .collection('budgets')
        .add(packageData);

      console.log('✅ [POST] Pacote salvo com ID:', packageRef.id);
    } catch (saveError: any) {
      console.error('❌ [POST] Erro detalhado ao salvar no Firebase:', {
        error: saveError.message,
        code: saveError.code,
        stack: saveError.stack,
        packageDataKeys: Object.keys(packageData),
        proposalId: body.proposalId,
        influencerId: body.influencerId
      });

      return NextResponse.json(
        {
          error: 'Erro ao salvar pacote no banco de dados',
          details: saveError.message
        },
        { status: 500 }
      );
    }

    // Retornar dados do pacote criado
    const createdPackage = {
      id: packageRef.id,
      ...packageData,
      createdAt: new Date(packageData.createdAt),
      updatedAt: new Date(packageData.updatedAt)
    };

    return NextResponse.json({
      success: true,
      message: 'Pacote de serviços criado com sucesso',
      package: createdPackage
    });

  } catch (error: any) {
    console.error('❌ [POST] Erro geral ao salvar pacote de serviços:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const proposalId = searchParams.get('proposalId');
    const influencerId = searchParams.get('influencerId');

    console.log('📊 [GET] Buscando pacotes de serviços:', { userId, proposalId, influencerId });

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    if (!proposalId || !influencerId) {
      return NextResponse.json(
        { error: 'proposalId e influencerId são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar acesso à proposta
    const proposalDoc = await db.collection('proposals').doc(proposalId).get();
    
    if (!proposalDoc.exists) {
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    const isOwner = proposalData?.criadoPor === userId;
    let isCollaborator = false;
    
    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) => 
        collab.email === userId || collab.userId === userId
      );
    }
    
    if (!isOwner && !isCollaborator) {
      return NextResponse.json({
        success: true,
        packages: [],
        total: 0,
        error: 'Acesso negado à proposta'
      });
    }

    // Buscar pacotes na subcoleção budgets (filtrar por type: 'package')
    const packagesSnapshot = await db
      .collection('proposals')
      .doc(proposalId)
      .collection('influencers')
      .doc(influencerId)
      .collection('budgets')
      .where('type', '==', 'package')
      .orderBy('updatedAt', 'desc')
      .get();

    const packages = packagesSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: new Date(data.createdAt || Date.now()),
        updatedAt: new Date(data.updatedAt || Date.now())
      };
    });

    console.log('✅ [GET] Pacotes encontrados:', packages.length);

    return NextResponse.json({
      success: true,
      packages,
      total: packages.length
    });

  } catch (error) {
    console.error('❌ [GET] Erro ao buscar pacotes:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
