import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { ServicePackage, PackageService } from '@/types/service-package';
import { ServiceType } from '@/types/budget';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [POST] Recebendo dados do pacote de serviços');
    
    const body: {
      name: string;
      description?: string;
      totalPrice: number;
      isTemplate?: boolean;
      category?: string;
      tags?: string[];
      minFollowers?: number;
      platforms: string[];
      services: PackageService[];
      influencerId: string;
      influencerName: string;
      proposalId: string;
      userId: string;
      brandId: string;
    } = await request.json();
    
    console.log('📦 Dados recebidos:', {
      name: body.name,
      influencerId: body.influencerId,
      proposalId: body.proposalId,
      servicesCount: body.services?.length || 0
    });

    // Validação de campos obrigatórios
    if (!body.name?.trim()) {
      return NextResponse.json(
        { error: 'Nome do pacote é obrigatório' },
        { status: 400 }
      );
    }

    if (!body.influencerId || !body.proposalId || !body.userId || !body.brandId) {
      return NextResponse.json(
        { error: 'Campos obrigatórios: influencerId, proposalId, userId, brandId' },
        { status: 400 }
      );
    }

    if (!body.services || body.services.length === 0) {
      return NextResponse.json(
        { error: 'Pelo menos um serviço deve ser selecionado' },
        { status: 400 }
      );
    }

    // Validar estrutura dos serviços
    for (const service of body.services) {
      if (!service.serviceType || !service.platform || !service.serviceName) {
        return NextResponse.json(
          { error: 'Cada serviço deve ter serviceType, platform e serviceName' },
          { status: 400 }
        );
      }

      if (service.quantity <= 0 || service.unitPrice < 0) {
        return NextResponse.json(
          { error: 'Quantidade deve ser maior que 0 e preço unitário não pode ser negativo' },
          { status: 400 }
        );
      }

      // Validar se totalPrice está correto
      const expectedTotal = service.quantity * service.unitPrice;
      if (Math.abs(service.totalPrice - expectedTotal) > 0.01) {
        return NextResponse.json(
          { error: `Preço total incorreto para o serviço ${service.serviceName}` },
          { status: 400 }
        );
      }
    }

    // Calcular preço total do pacote
    const calculatedTotalPrice = body.services.reduce((total, service) => total + service.totalPrice, 0);
    
    if (Math.abs(body.totalPrice - calculatedTotalPrice) > 0.01) {
      return NextResponse.json(
        { error: 'Preço total do pacote não confere com a soma dos serviços' },
        { status: 400 }
      );
    }

    // Verificar se a proposta existe e o usuário tem acesso
    const proposalDoc = await db.collection('proposals').doc(body.proposalId).get();
    
    if (!proposalDoc.exists) {
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    
    // Verificar permissões (owner ou colaborador)
    const isOwner = proposalData?.criadoPor === body.userId;
    let isCollaborator = false;
    
    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) => 
        collab.email === body.userId || collab.userId === body.userId
      );
    }
    
    if (!isOwner && !isCollaborator) {
      return NextResponse.json(
        { error: 'Acesso negado à proposta' },
        { status: 403 }
      );
    }

    // Preparar dados do pacote para salvar na subcoleção budgets
    const packageData = {
      // Identificação
      type: 'package', // Diferencia de budgets individuais
      packageName: body.name.trim(),
      description: body.description || '',
      
      // Relacionamentos
      influencerId: body.influencerId,
      influencerName: body.influencerName || '',
      userId: body.userId,
      brandId: body.brandId,
      proposalId: body.proposalId,
      
      // Dados financeiros
      amount: body.totalPrice,
      currency: 'BRL',
      
      // Serviços do pacote
      services: body.services,
      platforms: body.platforms || [...new Set(body.services.map(s => s.platform))],
      
      // Configurações do pacote
      isTemplate: body.isTemplate || false,
      category: body.category || '',
      tags: body.tags || [],
      minFollowers: body.minFollowers,
      
      // Status e workflow
      status: 'draft',
      serviceType: 'package' as ServiceType,
      
      // Contrapropostas (seguindo padrão dos budgets)
      counterProposals: [],
      hasCounterProposal: false,
      
      // Metadados
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.userId
    };

    console.log('💾 [POST] Salvando pacote na estrutura hierárquica:', {
      proposalId: body.proposalId,
      influencerId: body.influencerId,
      packageName: body.name
    });

    // Salvar na mesma estrutura dos budgets: proposals/{proposalId}/influencers/{influencerId}/budgets/{packageId}
    const packageRef = await db
      .collection('proposals')
      .doc(body.proposalId)
      .collection('influencers')
      .doc(body.influencerId)
      .collection('budgets')
      .add(packageData);

    console.log('✅ [POST] Pacote salvo com ID:', packageRef.id);

    // Retornar dados do pacote criado
    const createdPackage = {
      id: packageRef.id,
      ...packageData,
      createdAt: new Date(packageData.createdAt),
      updatedAt: new Date(packageData.updatedAt)
    };

    return NextResponse.json({
      success: true,
      message: 'Pacote de serviços criado com sucesso',
      package: createdPackage
    });

  } catch (error) {
    console.error('❌ [POST] Erro ao salvar pacote de serviços:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const proposalId = searchParams.get('proposalId');
    const influencerId = searchParams.get('influencerId');

    console.log('📊 [GET] Buscando pacotes de serviços:', { userId, proposalId, influencerId });

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    if (!proposalId || !influencerId) {
      return NextResponse.json(
        { error: 'proposalId e influencerId são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar acesso à proposta
    const proposalDoc = await db.collection('proposals').doc(proposalId).get();
    
    if (!proposalDoc.exists) {
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    const isOwner = proposalData?.criadoPor === userId;
    let isCollaborator = false;
    
    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) => 
        collab.email === userId || collab.userId === userId
      );
    }
    
    if (!isOwner && !isCollaborator) {
      return NextResponse.json({
        success: true,
        packages: [],
        total: 0,
        error: 'Acesso negado à proposta'
      });
    }

    // Buscar pacotes na subcoleção budgets (filtrar por type: 'package')
    const packagesSnapshot = await db
      .collection('proposals')
      .doc(proposalId)
      .collection('influencers')
      .doc(influencerId)
      .collection('budgets')
      .where('type', '==', 'package')
      .orderBy('updatedAt', 'desc')
      .get();

    const packages = packagesSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: new Date(data.createdAt || Date.now()),
        updatedAt: new Date(data.updatedAt || Date.now())
      };
    });

    console.log('✅ [GET] Pacotes encontrados:', packages.length);

    return NextResponse.json({
      success: true,
      packages,
      total: packages.length
    });

  } catch (error) {
    console.error('❌ [GET] Erro ao buscar pacotes:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
